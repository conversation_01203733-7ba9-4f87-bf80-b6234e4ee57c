<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Branding - School Setup</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <style>
      :root {
          --primary-color: {{ setup.primary_color or '#1f7d53' }};
          --secondary-color: {{ setup.secondary_color or '#18230f' }};
          --accent-color: {{ setup.accent_color or '#4ade80' }};
      }

      * { margin: 0; padding: 0; box-sizing: border-box; }

      body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
          min-height: 100vh;
          color: #1f2937;
      }

      .setup-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 2rem;
      }

      .setup-header {
          text-align: center;
          margin-bottom: 2rem;
          color: white;
      }

      .setup-header h1 {
          font-size: 2.5rem;
          margin-bottom: 0.5rem;
          font-weight: 700;
      }

      .setup-card {
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(20px);
          border-radius: 20px;
          padding: 3rem;
          box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
      }

      .form-section {
          margin-bottom: 2rem;
      }

      .form-section h3 {
          font-size: 1.3rem;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 1rem;
          padding-bottom: 0.5rem;
          border-bottom: 2px solid var(--primary-color);
      }

      .form-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 1.5rem;
      }

      .form-group {
          margin-bottom: 1.5rem;
      }

      .form-label {
          display: block;
          margin-bottom: 0.5rem;
          font-weight: 600;
          color: #374151;
      }

      .form-input, .form-file {
          width: 100%;
          padding: 0.75rem;
          border: 2px solid #d1d5db;
          border-radius: 8px;
          font-size: 1rem;
          transition: border-color 0.3s ease;
      }

      .form-input:focus, .form-file:focus {
          outline: none;
          border-color: var(--primary-color);
          box-shadow: 0 0 0 3px rgba(31, 125, 83, 0.1);
      }

      .color-input {
          height: 50px;
          padding: 0;
          border: 2px solid #d1d5db;
          border-radius: 8px;
          cursor: pointer;
      }

      .logo-preview {
          width: 100px;
          height: 100px;
          border: 2px dashed #d1d5db;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 1rem;
          background: #f9fafb;
      }

      .logo-preview img {
          max-width: 90px;
          max-height: 90px;
          object-fit: contain;
      }

      .color-preview {
          display: flex;
          gap: 1rem;
          margin-top: 1rem;
          padding: 1rem;
          background: #f9fafb;
          border-radius: 8px;
      }

      .color-sample {
          width: 50px;
          height: 50px;
          border-radius: 8px;
          border: 2px solid white;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }

      .form-actions {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 2rem;
          padding-top: 2rem;
          border-top: 1px solid #e5e7eb;
      }

      .btn {
          padding: 0.75rem 2rem;
          border: none;
          border-radius: 8px;
          font-size: 1rem;
          font-weight: 600;
          cursor: pointer;
          text-decoration: none;
          display: inline-flex;
          align-items: center;
          gap: 0.5rem;
          transition: all 0.3s ease;
      }

      .btn-primary {
          background: var(--primary-color);
          color: white;
      }

      .btn-primary:hover {
          background: var(--secondary-color);
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      }

      .btn-secondary {
          background: #6b7280;
          color: white;
      }

      .btn-secondary:hover {
          background: #4b5563;
      }

      .alert {
          padding: 1rem;
          border-radius: 8px;
          margin-bottom: 1.5rem;
          display: flex;
          align-items: center;
          gap: 0.5rem;
      }

      .alert-success {
          background: #d1fae5;
          color: #065f46;
          border: 1px solid #a7f3d0;
      }

      .alert-error {
          background: #fee2e2;
          color: #991b1b;
          border: 1px solid #fecaca;
      }

      .help-text {
          font-size: 0.875rem;
          color: #6b7280;
          margin-top: 0.25rem;
      }

      .back-link {
          display: inline-block;
          margin-bottom: 2rem;
          color: white;
          text-decoration: none;
          padding: 0.5rem 1rem;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 25px;
          transition: all 0.3s ease;
      }

      .back-link:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: translateX(-5px);
      }

      @media (max-width: 768px) {
          .setup-container { padding: 1rem; }
          .setup-card { padding: 2rem; }
          .form-grid { grid-template-columns: 1fr; }
          .form-actions { flex-direction: column; gap: 1rem; }
      }
    </style>
  </head>
  <body>
    <div class="setup-container">
      <a href="{{ url_for('school_setup.setup_dashboard') }}" class="back-link">
        <i class="fas fa-arrow-left"></i> Back to Setup Dashboard
      </a>

      <div class="setup-header">
        <h1><i class="fas fa-palette"></i> School Branding</h1>
        <p>Step 4 of 6 - Customize your school's visual identity</p>
      </div>

      <div class="setup-card">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %} {% if
        messages %} {% for category, message in messages %}
        <div
          class="alert alert-{{ 'error' if category == 'error' else 'success' }}"
        >
          <i
            class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }}"
          ></i>
          {{ message }}
        </div>
        {% endfor %} {% endif %} {% endwith %}

        <form method="POST" enctype="multipart/form-data">
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />
          <!-- Logo Upload Section -->
          <div class="form-section">
            <h3><i class="fas fa-image"></i> School Logo</h3>
            <div class="form-group">
              <label for="logo_file" class="form-label"
                >Upload School Logo</label
              >
              <input
                type="file"
                id="logo_file"
                name="logo_file"
                class="form-file"
                accept="image/*"
              />
              <div class="help-text">
                Upload your school logo (PNG, JPG, or SVG recommended)
              </div>

              <div class="logo-preview" id="logo-preview">
                {% if setup.logo_filename %}
                <img
                  src="/static/uploads/logos/{{ setup.logo_filename }}"
                  alt="School Logo"
                  id="logo-image"
                />
                {% else %}
                <i
                  class="fas fa-image"
                  style="font-size: 2rem; color: #9ca3af"
                  id="logo-placeholder"
                ></i>
                {% endif %}
              </div>
            </div>
          </div>

          <!-- Color Scheme Section -->
          <div class="form-section">
            <h3><i class="fas fa-palette"></i> Color Scheme</h3>
            <div class="form-grid">
              <div class="form-group">
                <label for="primary_color" class="form-label"
                  >Primary Color</label
                >
                <input
                  type="color"
                  id="primary_color"
                  name="primary_color"
                  class="color-input"
                  value="{{ setup.primary_color or '#1f7d53' }}"
                />
                <div class="help-text">
                  Main brand color used throughout the system
                </div>
              </div>

              <div class="form-group">
                <label for="secondary_color" class="form-label"
                  >Secondary Color</label
                >
                <input
                  type="color"
                  id="secondary_color"
                  name="secondary_color"
                  class="color-input"
                  value="{{ setup.secondary_color or '#18230f' }}"
                />
                <div class="help-text">Complementary color for accents</div>
              </div>

              <div class="form-group">
                <label for="accent_color" class="form-label"
                  >Accent Color</label
                >
                <input
                  type="color"
                  id="accent_color"
                  name="accent_color"
                  class="color-input"
                  value="{{ setup.accent_color or '#4ade80' }}"
                />
                <div class="help-text">
                  Color for highlights and interactive elements
                </div>
              </div>
            </div>

            <!-- Color Preview -->
            <div class="color-preview">
              <div
                class="color-sample"
                style="background-color: {{ setup.primary_color or '#1f7d53' }};"
                title="Primary Color"
              ></div>
              <div
                class="color-sample"
                style="background-color: {{ setup.secondary_color or '#18230f' }};"
                title="Secondary Color"
              ></div>
              <div
                class="color-sample"
                style="background-color: {{ setup.accent_color or '#4ade80' }};"
                title="Accent Color"
              ></div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="form-actions">
            <a
              href="{{ url_for('school_setup.academic_config') }}"
              class="btn btn-secondary"
            >
              <i class="fas fa-arrow-left"></i> Back to Academic Config
            </a>
            <button type="submit" class="btn btn-primary">
              Continue to Features <i class="fas fa-arrow-right"></i>
            </button>
          </div>
        </form>
      </div>
    </div>

    <script>
      // Logo preview functionality
      document
        .getElementById("logo_file")
        .addEventListener("change", function (event) {
          const file = event.target.files[0];
          const preview = document.getElementById("logo-preview");

          if (file) {
            // Check if file is an image
            if (file.type.startsWith("image/")) {
              const reader = new FileReader();

              reader.onload = function (e) {
                // Remove existing content
                preview.innerHTML = "";

                // Create new image element
                const img = document.createElement("img");
                img.src = e.target.result;
                img.alt = "School Logo Preview";
                img.id = "logo-image";
                img.style.maxWidth = "90px";
                img.style.maxHeight = "90px";
                img.style.objectFit = "contain";

                preview.appendChild(img);
              };

              reader.readAsDataURL(file);
            } else {
              alert("Please select a valid image file (PNG, JPG, JPEG, SVG)");
              event.target.value = "";
            }
          } else {
            // Reset to placeholder if no file selected
            preview.innerHTML =
              '<i class="fas fa-image" style="font-size: 2rem; color: #9ca3af" id="logo-placeholder"></i>';
          }
        });

      // Update color previews when colors change
      document
        .getElementById("primary_color")
        .addEventListener("change", function () {
          document.querySelector(
            ".color-sample:nth-child(1)"
          ).style.backgroundColor = this.value;
        });

      document
        .getElementById("secondary_color")
        .addEventListener("change", function () {
          document.querySelector(
            ".color-sample:nth-child(2)"
          ).style.backgroundColor = this.value;
        });

      document
        .getElementById("accent_color")
        .addEventListener("change", function () {
          document.querySelector(
            ".color-sample:nth-child(3)"
          ).style.backgroundColor = this.value;
        });
    </script>
  </body>
</html>
