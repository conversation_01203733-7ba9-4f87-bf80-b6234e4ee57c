"""
Authentication services for the Hillview School Management System.
"""
import logging
from ..models import Teacher

logger = logging.getLogger(__name__)

def authenticate_teacher(username, password, role):
    """
    Authenticate a teacher with the given credentials.

    Args:
        username: The teacher's username
        password: The teacher's password
        role: The teacher's role (headteacher, teacher, classteacher)

    Returns:
        Teacher object if authentication is successful, None otherwise
    """
    try:
        # Find teacher by username and role first
        teacher = Teacher.query.filter_by(username=username, role=role).first()

        if teacher and teacher.check_password(password):
            logger.info("Authentication successful for %s with role %s", username, role)
            return teacher
        else:
            logger.warning("Authentication failed for %s with role %s", username, role)
            return None

    except Exception as e:
        logger.error(f"Authentication error: {e}")
        # If there's a database error, try to initialize the database
        try:
            from ..utils.database_init import (
                check_database_integrity,
                initialize_database_completely
            )

            status = check_database_integrity()
            if status['status'] != 'healthy':
                logger.info("Database not healthy, attempting initialization...")
                result = initialize_database_completely()
                if result['success']:
                    logger.info("Database initialized successfully, retrying authentication...")
                    # Retry authentication after database initialization
                    teacher = Teacher.query.filter_by(username=username, role=role).first()
                    if teacher and teacher.check_password(password):
                        return teacher
        except Exception as init_error:
            logger.error("Database initialization error: %s", init_error)

        return None

def get_teacher_by_id(teacher_id):
    """
    Get a teacher by ID.
    
    Args:
        teacher_id: The teacher's ID
        
    Returns:
        Teacher object if found, None otherwise
    """
    return Teacher.query.get(teacher_id)

def is_authenticated(session):
    """
    Check if a user is authenticated based on session data.
    
    Args:
        session: The Flask session object
        
    Returns:
        Boolean indicating if the user is authenticated
    """
    return 'teacher_id' in session

def get_role(session):
    """
    Get the role of the authenticated user.
    
    Args:
        session: The Flask session object
        
    Returns:
        String representing the user's role, or None if not authenticated
    """
    return session.get('role')

def logout(session):
    """
    Log out the current user by clearing the session.
    
    Args:
        session: The Flask session object
    """
    session.clear()