<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Basic Information - School Setup</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <style>
      :root {
        --primary-color: #1f7d53;
        --secondary-color: #18230f;
        --accent-color: #4ade80;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --error-color: #ef4444;
        --gray-50: #f9fafb;
        --gray-100: #f3f4f6;
        --gray-200: #e5e7eb;
        --gray-300: #d1d5db;
        --gray-400: #9ca3af;
        --gray-500: #6b7280;
        --gray-600: #4b5563;
        --gray-700: #374151;
        --gray-800: #1f2937;
        --gray-900: #111827;
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(
          135deg,
          var(--primary-color) 0%,
          var(--secondary-color) 100%
        );
        min-height: 100vh;
        color: var(--gray-800);
      }

      .setup-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
      }

      .setup-header {
        text-align: center;
        margin-bottom: 2rem;
        color: white;
      }

      .setup-header h1 {
        font-size: 2.5rem;
        margin-bottom: 0.5rem;
        font-weight: 700;
      }

      .setup-header p {
        font-size: 1.1rem;
        opacity: 0.9;
      }

      .step-indicator {
        display: flex;
        justify-content: center;
        margin-bottom: 2rem;
      }

      .step {
        display: flex;
        align-items: center;
        color: white;
        opacity: 0.5;
      }

      .step.active {
        opacity: 1;
      }

      .step-number {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        margin-right: 0.5rem;
      }

      .step.active .step-number {
        background: white;
        color: var(--primary-color);
      }

      .setup-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 3rem;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
      }

      .form-section {
        margin-bottom: 2rem;
      }

      .form-section h3 {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--gray-800);
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid var(--primary-color);
      }

      .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
      }

      .form-group {
        margin-bottom: 1.5rem;
      }

      .form-group.full-width {
        grid-column: 1 / -1;
      }

      .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: var(--gray-700);
      }

      .form-label.required::after {
        content: " *";
        color: var(--error-color);
      }

      .form-input {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid var(--gray-300);
        border-radius: 8px;
        font-size: 1rem;
        transition: border-color 0.3s ease;
      }

      .form-input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(31, 125, 83, 0.1);
      }

      .form-textarea {
        min-height: 100px;
        resize: vertical;
      }

      .form-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid var(--gray-200);
      }

      .btn {
        padding: 0.75rem 2rem;
        border: none;
        border-radius: 8px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
      }

      .btn-primary {
        background: var(--primary-color);
        color: white;
      }

      .btn-primary:hover {
        background: var(--secondary-color);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      }

      .btn-secondary {
        background: var(--gray-500);
        color: white;
      }

      .btn-secondary:hover {
        background: var(--gray-600);
      }

      .alert {
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .alert-success {
        background: #d1fae5;
        color: #065f46;
        border: 1px solid #a7f3d0;
      }

      .alert-error {
        background: #fee2e2;
        color: #991b1b;
        border: 1px solid #fecaca;
      }

      .help-text {
        font-size: 0.875rem;
        color: var(--gray-600);
        margin-top: 0.25rem;
      }

      .back-link {
        display: inline-block;
        margin-bottom: 2rem;
        color: white;
        text-decoration: none;
        padding: 0.5rem 1rem;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 25px;
        transition: all 0.3s ease;
      }

      .back-link:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateX(-5px);
      }

      @media (max-width: 768px) {
        .setup-container {
          padding: 1rem;
        }

        .setup-card {
          padding: 2rem;
        }

        .form-grid {
          grid-template-columns: 1fr;
        }

        .form-actions {
          flex-direction: column;
          gap: 1rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="setup-container">
      <a href="{{ url_for('school_setup.setup_dashboard') }}" class="back-link">
        <i class="fas fa-arrow-left"></i> Back to Setup Dashboard
      </a>

      <div class="setup-header">
        <h1><i class="fas fa-info-circle"></i> Basic Information</h1>
        <p>Step 1 of 6 - Enter your school's basic information</p>
      </div>

      <div class="step-indicator">
        <div class="step active">
          <div class="step-number">1</div>
          <span>Basic Info</span>
        </div>
      </div>

      <div class="setup-card">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %} {% if
        messages %} {% for category, message in messages %}
        <div
          class="alert alert-{{ 'error' if category == 'error' else 'success' }}"
        >
          <i
            class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }}"
          ></i>
          {{ message }}
        </div>
        {% endfor %} {% endif %} {% endwith %}

        <form method="POST">
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />
          <!-- School Identity Section -->
          <div class="form-section">
            <h3><i class="fas fa-school"></i> School Identity</h3>
            <div class="form-grid">
              <div class="form-group">
                <label for="school_name" class="form-label required"
                  >School Name</label
                >
                <input
                  type="text"
                  id="school_name"
                  name="school_name"
                  class="form-input"
                  value="{{ setup.school_name or '' }}"
                  required
                  placeholder="e.g., Hillview Primary School"
                />
                <div class="help-text">The official name of your school</div>
              </div>

              <div class="form-group">
                <label for="school_motto" class="form-label"
                  >School Motto</label
                >
                <input
                  type="text"
                  id="school_motto"
                  name="school_motto"
                  class="form-input"
                  value="{{ setup.school_motto or '' }}"
                  placeholder="e.g., Excellence in Education"
                />
                <div class="help-text">Your school's motto or slogan</div>
              </div>
            </div>

            <div class="form-group full-width">
              <label for="school_vision" class="form-label"
                >School Vision</label
              >
              <textarea
                id="school_vision"
                name="school_vision"
                class="form-input form-textarea"
                placeholder="e.g., To be a leading institution in providing quality education..."
              >
{{ setup.school_vision or '' }}</textarea
              >
              <div class="help-text">
                Your school's long-term vision statement
              </div>
            </div>

            <div class="form-group full-width">
              <label for="school_mission" class="form-label"
                >School Mission</label
              >
              <textarea
                id="school_mission"
                name="school_mission"
                class="form-input form-textarea"
                placeholder="e.g., To nurture learners to become responsible citizens..."
              >
{{ setup.school_mission or '' }}</textarea
              >
              <div class="help-text">Your school's mission statement</div>
            </div>
          </div>

          <!-- Contact Information Section -->
          <div class="form-section">
            <h3><i class="fas fa-phone"></i> Contact Information</h3>
            <div class="form-grid">
              <div class="form-group">
                <label for="school_phone" class="form-label"
                  >Phone Number</label
                >
                <input
                  type="tel"
                  id="school_phone"
                  name="school_phone"
                  class="form-input"
                  value="{{ setup.school_phone or '' }}"
                  placeholder="e.g., +254 123 456789"
                />
                <div class="help-text">Main school phone number</div>
              </div>

              <div class="form-group">
                <label for="school_mobile" class="form-label"
                  >Mobile Number</label
                >
                <input
                  type="tel"
                  id="school_mobile"
                  name="school_mobile"
                  class="form-input"
                  value="{{ setup.school_mobile or '' }}"
                  placeholder="e.g., +254 700 123456"
                />
                <div class="help-text">School mobile number</div>
              </div>

              <div class="form-group">
                <label for="school_email" class="form-label"
                  >Email Address</label
                >
                <input
                  type="email"
                  id="school_email"
                  name="school_email"
                  class="form-input"
                  value="{{ setup.school_email or '' }}"
                  placeholder="e.g., <EMAIL>"
                />
                <div class="help-text">Official school email address</div>
              </div>

              <div class="form-group">
                <label for="school_website" class="form-label">Website</label>
                <input
                  type="url"
                  id="school_website"
                  name="school_website"
                  class="form-input"
                  value="{{ setup.school_website or '' }}"
                  placeholder="e.g., www.hillviewschool.ac.ke"
                />
                <div class="help-text">School website URL (optional)</div>
              </div>
            </div>
          </div>

          <!-- Address Information Section -->
          <div class="form-section">
            <h3><i class="fas fa-map-marker-alt"></i> Address Information</h3>
            <div class="form-group full-width">
              <label for="school_address" class="form-label"
                >Physical Address</label
              >
              <textarea
                id="school_address"
                name="school_address"
                class="form-input form-textarea"
                placeholder="e.g., 123 Education Street, Nairobi"
              >
{{ setup.school_address or '' }}</textarea
              >
              <div class="help-text">
                Complete physical address of the school
              </div>
            </div>

            <div class="form-group">
              <label for="postal_address" class="form-label"
                >Postal Address</label
              >
              <input
                type="text"
                id="postal_address"
                name="postal_address"
                class="form-input"
                value="{{ setup.postal_address or '' }}"
                placeholder="e.g., P.O. Box 123, Nairobi"
              />
              <div class="help-text">Postal address including P.O. Box</div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="form-actions">
            <a
              href="{{ url_for('school_setup.setup_dashboard') }}"
              class="btn btn-secondary"
            >
              <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
            <button type="submit" class="btn btn-primary">
              Continue to Registration Info <i class="fas fa-arrow-right"></i>
            </button>
          </div>
        </form>
      </div>
    </div>
  </body>
</html>
