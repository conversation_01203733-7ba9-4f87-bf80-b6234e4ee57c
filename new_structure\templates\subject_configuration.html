<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Subject Configuration - {{ school_name }}</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        padding: 20px;
      }

      .config-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(15px);
        border-radius: 25px;
        box-shadow: 0 20px 40px rgba(31, 38, 135, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.2);
        max-width: 1400px;
        margin: 0 auto;
        padding: 40px;
      }

      .config-header {
        text-align: center;
        margin-bottom: 40px;
        color: #2c3e50;
      }

      .config-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .config-header .lead {
        font-size: 1.1rem;
        color: #6c757d;
        font-weight: 400;
      }

      /* Flexbox Grid Layout */
      .config-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 25px;
        justify-content: center;
        margin-bottom: 40px;
      }

      .config-card {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 20px;
        padding: 30px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        flex: 1 1 400px;
        max-width: 450px;
        min-height: 320px;
        display: flex;
        flex-direction: column;
        position: relative;
        overflow: hidden;
      }

      .config-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2);
        border-radius: 20px 20px 0 0;
      }

      .config-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        background: rgba(255, 255, 255, 0.95);
      }

      /* Card Header Styling */
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;
        padding-bottom: 15px;
        border-bottom: 2px solid rgba(102, 126, 234, 0.1);
      }

      .subject-name {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2c3e50;
        text-transform: capitalize;
        margin: 0;
      }

      .education-level-badge {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 8px 16px;
        border-radius: 25px;
        font-size: 0.85rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
      }

      /* Toggle Section */
      .toggle-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;
        padding: 20px;
        background: rgba(102, 126, 234, 0.05);
        border-radius: 15px;
        border: 1px solid rgba(102, 126, 234, 0.1);
      }

      .toggle-label {
        font-size: 1.1rem;
        font-weight: 600;
        color: #495057;
      }

      .toggle-controls {
        display: flex;
        align-items: center;
        gap: 15px;
      }

      .status-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .status-composite {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
      }

      .status-simple {
        background: linear-gradient(135deg, #dc3545, #fd7e14);
        color: white;
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
      }

      /* Modern Toggle Switch */
      .toggle-switch {
        position: relative;
        display: inline-block;
        width: 70px;
        height: 38px;
      }

      .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
      }

      .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, #e9ecef, #dee2e6);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 38px;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .slider:before {
        position: absolute;
        content: "";
        height: 30px;
        width: 30px;
        left: 4px;
        bottom: 4px;
        background: linear-gradient(135deg, #ffffff, #f8f9fa);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 50%;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }

      input:checked + .slider {
        background: linear-gradient(135deg, #667eea, #764ba2);
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
      }

      input:checked + .slider:before {
        transform: translateX(32px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      }

      /* Component Information */
      .component-info {
        background: linear-gradient(
          135deg,
          rgba(40, 167, 69, 0.05),
          rgba(25, 135, 84, 0.05)
        );
        border-radius: 15px;
        padding: 20px;
        margin-top: 20px;
        border: 1px solid rgba(40, 167, 69, 0.2);
        backdrop-filter: blur(10px);
      }

      .info-text {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        font-size: 0.95rem;
        color: #495057;
        line-height: 1.5;
      }

      .info-text i {
        color: #28a745;
        margin-top: 2px;
        font-size: 1.1rem;
      }

      .info-text strong {
        color: #198754;
        font-weight: 600;
      }

      /* Action Buttons */
      .action-buttons {
        text-align: center;
        margin-top: 30px;
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
      }

      .btn-primary {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        border-radius: 25px;
        padding: 15px 30px;
        font-weight: 600;
        font-size: 1rem;
        color: white;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 10px;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
      }

      .btn-primary:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        color: white;
      }

      .btn-secondary {
        background: linear-gradient(135deg, #6c757d, #495057);
        border: none;
        border-radius: 25px;
        padding: 15px 30px;
        font-weight: 600;
        font-size: 1rem;
        color: white;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 10px;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
      }

      .btn-secondary:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(108, 117, 125, 0.4);
        color: white;
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .config-container {
          padding: 20px;
          margin: 10px;
        }

        .config-grid {
          gap: 20px;
        }

        .config-card {
          flex: 1 1 100%;
          max-width: none;
        }

        .card-header {
          flex-direction: column;
          gap: 15px;
          text-align: center;
        }

        .toggle-section {
          flex-direction: column;
          gap: 15px;
          text-align: center;
        }

        .component-row {
          flex-direction: column;
          gap: 15px;
        }

        .action-buttons {
          flex-direction: column;
          align-items: center;
        }
      }
    </style>
  </head>
  <body>
    <div class="config-container">
      <div class="config-header">
        <h1><i class="fas fa-cogs"></i> Subject Configuration</h1>
        <p class="lead">
          Toggle English and Kiswahili between composite and simple modes. Raw
          marks for composite subjects are entered during marks upload.
        </p>
      </div>

      <div class="config-grid">
        {% for config in configurations %}
        <div
          class="config-card"
          data-subject="{{ config.subject_name }}"
          data-level="{{ config.education_level }}"
        >
          <!-- Card Header -->
          <div class="card-header">
            <h3 class="subject-name">{{ config.subject_name }}</h3>
            <span class="education-level-badge">
              {{ config.education_level.replace('_', ' ').title() }}
            </span>
          </div>

          <!-- Toggle Section -->
          <div class="toggle-section">
            <span class="toggle-label">Composite Mode:</span>
            <div class="toggle-controls">
              <span
                class="status-badge {% if config.is_composite %}status-composite{% else %}status-simple{% endif %}"
              >
                {% if config.is_composite %}Composite{% else %}Simple{% endif %}
              </span>
              <label class="toggle-switch">
                <input
                  type="checkbox"
                  {%
                  if
                  config.is_composite
                  %}checked{%
                  endif
                  %}
                  onchange="toggleComposite('{{ config.subject_name }}', '{{ config.education_level }}', this.checked)"
                />
                <span class="slider"></span>
              </label>
            </div>
          </div>

          <!-- Component Information (Read-only) -->
          {% if config.is_composite %}
          <div
            class="component-info"
            id="components-{{ config.subject_name }}-{{ config.education_level }}"
          >
            <div class="info-text">
              <i class="fas fa-info-circle"></i>
              <span>
                When composite mode is enabled, this subject will show
                <strong>{{ config.component_1_name }}</strong> and
                <strong>{{ config.component_2_name }}</strong>
                input fields in marks upload for entering raw marks.
              </span>
            </div>
          </div>
          {% endif %}
        </div>
        {% endfor %}
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <button id="saveAllBtn" class="btn-primary" type="button">
          <i class="fas fa-save"></i> Save All Configurations
        </button>

        <a href="{{ url_for('universal.dashboard') }}" class="btn-secondary">
          <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
      // Add event listener when DOM is loaded
      document.addEventListener("DOMContentLoaded", function () {
        console.log("🚀 Subject Configuration page loaded");

        const saveBtn = document.getElementById("saveAllBtn");
        if (saveBtn) {
          console.log("✅ Save button found, adding event listener");
          saveBtn.addEventListener("click", function (e) {
            e.preventDefault();
            console.log("🔄 Save button clicked via event listener");
            saveAllConfigurations();
          });
        } else {
          console.error("❌ Save button not found!");
        }
      });
    </script>
    <script>
      function toggleComposite(subjectName, educationLevel, isComposite) {
        console.log(
          `Toggling ${subjectName} (${educationLevel}) to ${
            isComposite ? "composite" : "simple"
          }`
        );

        // Update UI immediately
        const card = document.querySelector(
          `[data-subject="${subjectName}"][data-level="${educationLevel}"]`
        );
        const statusBadge = card?.querySelector(".status-badge");
        const componentsDiv = document.getElementById(
          `components-${subjectName}-${educationLevel}`
        );

        if (statusBadge) {
          if (isComposite) {
            statusBadge.textContent = "Composite";
            statusBadge.className = "status-badge status-composite me-2";
          } else {
            statusBadge.textContent = "Simple";
            statusBadge.className = "status-badge status-simple me-2";
          }
        }

        // Show/hide component info
        if (componentsDiv) {
          componentsDiv.style.display = isComposite ? "block" : "none";
        }

        // Send update to server
        fetch("/api/subject-config/toggle", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            subject_name: subjectName,
            education_level: educationLevel,
            is_composite: isComposite,
          }),
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.success) {
              showNotification(
                "Configuration updated successfully!",
                "success"
              );
            } else {
              showNotification(
                "Failed to update configuration: " + data.message,
                "error"
              );
            }
          })
          .catch((error) => {
            console.error("Error:", error);
            showNotification("Error updating configuration", "error");
          });
      }

      function saveAllConfigurations() {
        console.log("🔄 Save All Configurations button clicked");

        // Show immediate feedback
        showNotification("Saving all configurations...", "info");

        // Disable button to prevent double-clicks
        const saveBtn = document.getElementById("saveAllBtn");
        if (saveBtn) {
          saveBtn.disabled = true;
          saveBtn.innerHTML =
            '<i class="fas fa-spinner fa-spin"></i> Saving...';
        }

        // Get CSRF token if available
        const csrfToken = document
          .querySelector("meta[name=csrf-token]")
          ?.getAttribute("content");
        const headers = {
          "Content-Type": "application/json",
        };

        if (csrfToken) {
          headers["X-CSRFToken"] = csrfToken;
          console.log("🔐 CSRF token found and added");
        } else {
          console.log("⚠️ No CSRF token found");
        }

        console.log("📡 Making API request to /api/subject-config/save-all");

        fetch("/api/subject-config/save-all", {
          method: "POST",
          headers: headers,
        })
          .then((response) => {
            console.log(
              "📥 Response received:",
              response.status,
              response.statusText
            );
            if (!response.ok) {
              throw new Error(
                `HTTP ${response.status}: ${response.statusText}`
              );
            }
            return response.json();
          })
          .then((data) => {
            console.log("📊 Response data:", data);
            if (data.success) {
              showNotification(
                data.message ||
                  "✅ Configurations Saved! Composite subjects will now appear in marks upload for entering raw marks based on education level.",
                "success"
              );
            } else {
              showNotification(
                "Failed to save configurations: " + data.message,
                "error"
              );
            }
          })
          .catch((error) => {
            console.error("❌ Error:", error);
            showNotification(
              "Error saving configurations: " + error.message,
              "error"
            );
          })
          .finally(() => {
            // Re-enable button
            const saveBtn = document.getElementById("saveAllBtn");
            if (saveBtn) {
              saveBtn.disabled = false;
              saveBtn.innerHTML =
                '<i class="fas fa-save"></i> Save All Configurations';
            }
          });
      }

      function showNotification(message, type) {
        // Remove any existing notifications first
        const existingNotifications = document.querySelectorAll(
          ".custom-notification"
        );
        existingNotifications.forEach((n) => n.remove());

        // Create notification with better visibility
        const notification = document.createElement("div");
        notification.className = `custom-notification alert alert-${
          type === "success" ? "success" : type === "error" ? "danger" : "info"
        }`;

        notification.style.cssText = `
          position: fixed;
          top: 10px;
          left: 10px;
          right: 10px;
          z-index: 99999;
          margin: 0 auto;
          max-width: 95%;
          width: auto;
          box-shadow: 0 8px 25px rgba(0,0,0,0.4);
          border-radius: 12px;
          font-size: 16px;
          font-weight: 500;
          padding: 20px 25px;
          border: 2px solid ${
            type === "success"
              ? "#28a745"
              : type === "error"
              ? "#dc3545"
              : "#17a2b8"
          };
          background: ${
            type === "success"
              ? "#d4edda"
              : type === "error"
              ? "#f8d7da"
              : "#d1ecf1"
          };
          color: ${
            type === "success"
              ? "#155724"
              : type === "error"
              ? "#721c24"
              : "#0c5460"
          };
          animation: slideDownBounce 0.5s ease-out;
          text-align: left;
          line-height: 1.5;
        `;

        // Add enhanced CSS animations
        if (!document.getElementById("enhanced-notification-styles")) {
          const style = document.createElement("style");
          style.id = "enhanced-notification-styles";
          style.textContent = `
            @keyframes slideDownBounce {
              0% {
                transform: translateY(-100%);
                opacity: 0;
              }
              60% {
                transform: translateY(10px);
                opacity: 1;
              }
              100% {
                transform: translateY(0);
                opacity: 1;
              }
            }
            @keyframes slideUpFade {
              0% {
                transform: translateY(0);
                opacity: 1;
              }
              100% {
                transform: translateY(-100%);
                opacity: 0;
              }
            }
            .custom-notification .close-btn {
              background: none;
              border: none;
              font-size: 28px;
              font-weight: bold;
              color: inherit;
              cursor: pointer;
              padding: 0;
              margin-left: 15px;
              opacity: 0.7;
              transition: opacity 0.2s;
              line-height: 1;
            }
            .custom-notification .close-btn:hover {
              opacity: 1;
              transform: scale(1.1);
            }
          `;
          document.head.appendChild(style);
        }

        notification.innerHTML = `
          <div style="display: flex; align-items: flex-start; gap: 15px; width: 100%;">
            <i class="fas fa-${
              type === "success"
                ? "check-circle"
                : type === "error"
                ? "exclamation-triangle"
                : "info-circle"
            }" style="font-size: 24px; margin-top: 2px; flex-shrink: 0;"></i>
            <div style="flex: 1; word-wrap: break-word; overflow-wrap: break-word;">
              ${message}
            </div>
            <button type="button" class="close-btn" onclick="closeNotification(this)" title="Click to close">
              ×
            </button>
          </div>
        `;

        document.body.appendChild(notification);

        // NO auto-remove - user must click X to close
        // This ensures they can read the full message
      }

      function closeNotification(button) {
        const notification = button.closest(".custom-notification");
        if (notification) {
          notification.style.animation = "slideUpFade 0.3s ease-in forwards";
          setTimeout(() => {
            if (notification.parentElement) {
              notification.remove();
            }
          }, 300);
        }
      }
    </script>
  </body>
</html>
