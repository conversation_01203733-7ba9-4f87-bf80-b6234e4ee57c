<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Permission Management - Hillview School</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            color: white;
            font-size: 2.5rem;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }

        .stat-card i {
            font-size: 2.5rem;
            color: #4CAF50;
            margin-bottom: 15px;
        }

        .stat-card h3 {
            color: white;
            font-size: 2rem;
            margin-bottom: 5px;
        }

        .stat-card p {
            color: rgba(255, 255, 255, 0.8);
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .panel h2 {
            color: white;
            font-size: 1.5rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            color: white;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1rem;
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .form-control:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(244, 67, 54, 0.4);
        }

        .function-category {
            margin-bottom: 25px;
        }

        .category-header {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .category-header:hover {
            background: rgba(255, 255, 255, 0.15);
        }

        .category-header h3 {
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .function-list {
            display: none;
            padding-left: 20px;
        }

        .function-list.active {
            display: block;
        }

        .function-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 15px;
            margin-bottom: 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }

        .function-item.restricted {
            border-left-color: #f44336;
        }

        .function-name {
            color: white;
            font-weight: 500;
        }

        .function-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-allowed {
            background: rgba(76, 175, 80, 0.2);
            color: #4CAF50;
        }

        .status-restricted {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
        }

        .permission-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .permission-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            margin-bottom: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }

        .permission-info {
            flex: 1;
        }

        .permission-teacher {
            color: white;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .permission-function {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .permission-scope {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.8rem;
        }

        .permission-actions {
            display: flex;
            gap: 10px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 0.8rem;
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: none;
        }

        .alert-success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
            color: #4CAF50;
        }

        .alert-error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.3);
            color: #f44336;
        }

        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
        }

        .checkbox-item input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }

        .checkbox-item label {
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9rem;
            margin: 0;
            cursor: pointer;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>
                <i class="fas fa-shield-alt"></i>
                Enhanced Permission Management
            </h1>
            <p>Manage function-level permissions for classteachers with granular control</p>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <i class="fas fa-users"></i>
                <h3 id="total-teachers">{{ data.teachers|length }}</h3>
                <p>Total Teachers</p>
            </div>
            <div class="stat-card">
                <i class="fas fa-key"></i>
                <h3 id="total-permissions">{{ data.function_permissions|length }}</h3>
                <p>Active Permissions</p>
            </div>
            <div class="stat-card">
                <i class="fas fa-check-circle"></i>
                <h3 id="default-functions">{{ data.available_functions.default_allowed|length }}</h3>
                <p>Default Functions</p>
            </div>
            <div class="stat-card">
                <i class="fas fa-lock"></i>
                <h3 id="restricted-functions">{{ data.available_functions.restricted|length }}</h3>
                <p>Restricted Functions</p>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Grant Permissions Panel -->
            <div class="panel">
                <h2>
                    <i class="fas fa-plus-circle"></i>
                    Grant Function Permissions
                </h2>

                <div class="alert alert-success" id="success-alert"></div>
                <div class="alert alert-error" id="error-alert"></div>

                <form id="grant-permission-form">
                    <div class="form-group">
                        <label for="teacher-select">Select Teacher:</label>
                        <select class="form-control" id="teacher-select" required>
                            <option value="">Choose a teacher...</option>
                            {% for teacher in data.teachers %}
                            <option value="{{ teacher.id }}">{{ teacher.name }} ({{ teacher.username }})</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="scope-select">Permission Scope:</label>
                        <select class="form-control" id="scope-select">
                            <option value="global">Global (All Classes)</option>
                            <option value="grade">Specific Grade</option>
                            <option value="stream">Specific Stream</option>
                        </select>
                    </div>

                    <div class="form-group" id="grade-group" style="display: none;">
                        <label for="grade-select">Select Grade:</label>
                        <select class="form-control" id="grade-select">
                            <option value="">Choose a grade...</option>
                            {% for grade in data.grades %}
                            <option value="{{ grade.id }}">{{ grade.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="form-group" id="stream-group" style="display: none;">
                        <label for="stream-select">Select Stream:</label>
                        <select class="form-control" id="stream-select">
                            <option value="">Choose a stream...</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>Select Functions to Grant:</label>
                        <div id="function-categories">
                            {% for category, functions in data.available_functions.restricted.items() %}
                            <div class="function-category">
                                <div class="category-header" onclick="toggleCategory('{{ category }}')">
                                    <h3>
                                        {{ category.replace('_', ' ').title() }}
                                        <i class="fas fa-chevron-down" id="icon-{{ category }}"></i>
                                    </h3>
                                </div>
                                <div class="function-list" id="list-{{ category }}">
                                    <div class="checkbox-group">
                                        {% for function in functions %}
                                        <div class="checkbox-item">
                                            <input type="checkbox" id="func-{{ function }}" name="functions" value="{{ function }}">
                                            <label for="func-{{ function }}">{{ function.replace('_', ' ').title() }}</label>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="notes">Notes (Optional):</label>
                        <textarea class="form-control" id="notes" rows="3" placeholder="Add notes about this permission grant..."></textarea>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Grant Permissions
                    </button>
                </form>
            </div>

            <!-- Current Permissions Panel -->
            <div class="panel">
                <h2>
                    <i class="fas fa-list"></i>
                    Current Function Permissions
                </h2>

                <div class="permission-list" id="permission-list">
                    {% for permission in data.function_permissions %}
                    <div class="permission-item" data-permission-id="{{ permission.id }}">
                        <div class="permission-info">
                            <div class="permission-teacher">{{ permission.teacher_name }}</div>
                            <div class="permission-function">{{ permission.function_name.replace('_', ' ').title() }}</div>
                            <div class="permission-scope">
                                Scope: {{ permission.scope_type.title() }}
                                {% if permission.grade_name %} - {{ permission.grade_name }}{% endif %}
                                {% if permission.stream_name %} {{ permission.stream_name }}{% endif %}
                            </div>
                        </div>
                        <div class="permission-actions">
                            <button class="btn btn-danger btn-small" onclick="revokePermission({{ permission.id }}, '{{ permission.teacher_name }}', '{{ permission.function_name }}')">
                                <i class="fas fa-times"></i>
                                Revoke
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <script>
        // Toggle function category visibility
        function toggleCategory(category) {
            const list = document.getElementById(`list-${category}`);
            const icon = document.getElementById(`icon-${category}`);
            
            if (list.classList.contains('active')) {
                list.classList.remove('active');
                icon.style.transform = 'rotate(0deg)';
            } else {
                list.classList.add('active');
                icon.style.transform = 'rotate(180deg)';
            }
        }

        // Handle scope selection
        document.getElementById('scope-select').addEventListener('change', function() {
            const scope = this.value;
            const gradeGroup = document.getElementById('grade-group');
            const streamGroup = document.getElementById('stream-group');
            
            if (scope === 'grade' || scope === 'stream') {
                gradeGroup.style.display = 'block';
            } else {
                gradeGroup.style.display = 'none';
            }
            
            if (scope === 'stream') {
                streamGroup.style.display = 'block';
            } else {
                streamGroup.style.display = 'none';
            }
        });

        // Handle grade selection for streams
        document.getElementById('grade-select').addEventListener('change', function() {
            const gradeId = this.value;
            const streamSelect = document.getElementById('stream-select');
            
            // Clear existing options
            streamSelect.innerHTML = '<option value="">Choose a stream...</option>';
            
            if (gradeId) {
                // Filter streams for selected grade
                const streams = {{ data.streams|tojson }};
                streams.filter(stream => stream.grade_id == gradeId).forEach(stream => {
                    const option = document.createElement('option');
                    option.value = stream.id;
                    option.textContent = stream.name;
                    streamSelect.appendChild(option);
                });
            }
        });

        // Handle form submission
        document.getElementById('grant-permission-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const teacherId = document.getElementById('teacher-select').value;
            const scope = document.getElementById('scope-select').value;
            const gradeId = document.getElementById('grade-select').value;
            const streamId = document.getElementById('stream-select').value;
            const notes = document.getElementById('notes').value;
            
            // Get selected functions
            const selectedFunctions = Array.from(document.querySelectorAll('input[name="functions"]:checked'))
                .map(cb => cb.value);
            
            if (!teacherId || selectedFunctions.length === 0) {
                showAlert('Please select a teacher and at least one function.', 'error');
                return;
            }
            
            // Prepare data
            const data = {
                teacher_id: parseInt(teacherId),
                function_names: selectedFunctions,
                scope_type: scope,
                grade_id: scope !== 'global' ? parseInt(gradeId) || null : null,
                stream_id: scope === 'stream' ? parseInt(streamId) || null : null,
                notes: notes
            };
            
            // Submit request
            fetch('/permission/bulk_grant_functions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showAlert(result.message, 'success');
                    // Reset form
                    this.reset();
                    // Reload permissions
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showAlert(result.message, 'error');
                }
            })
            .catch(error => {
                showAlert('Error granting permissions: ' + error.message, 'error');
            });
        });

        // Revoke permission
        function revokePermission(permissionId, teacherName, functionName) {
            if (!confirm(`Are you sure you want to revoke '${functionName}' permission from ${teacherName}?`)) {
                return;
            }
            
            // Implementation for revoke would go here
            showAlert('Revoke functionality will be implemented', 'error');
        }

        // Show alert
        function showAlert(message, type) {
            const alertElement = document.getElementById(type === 'success' ? 'success-alert' : 'error-alert');
            alertElement.textContent = message;
            alertElement.style.display = 'block';
            
            setTimeout(() => {
                alertElement.style.display = 'none';
            }, 5000);
        }

        // Initialize - expand first category
        document.addEventListener('DOMContentLoaded', function() {
            const firstCategory = document.querySelector('.function-category .category-header');
            if (firstCategory) {
                firstCategory.click();
            }
        });
    </script>
</body>
</html>
