<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Manage Teacher Assignments - Hillview School</title>
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/style.css') }}"
    />
    <style>
      /* Override container styles for manage pages */
      .manage-container {
        max-width: 95% !important;
        width: 95% !important;
        margin: 80px auto 60px !important;
        padding: var(--spacing-xl) !important;
      }

      /* Page header styling */
      .page-header {
        background: var(--white);
        padding: var(--spacing-lg);
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
        margin-bottom: var(--spacing-xl);
      }

      .page-header h1 {
        color: var(--primary-color);
        margin-bottom: var(--spacing-sm);
        font-size: 2.5rem;
      }

      .nav-links a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
        margin-right: var(--spacing-md);
      }

      .nav-links a:hover {
        text-decoration: underline;
      }

      /* Tab styling */
      .tab {
        overflow: hidden;
        border: 1px solid var(--border-color);
        background-color: var(--background-color);
        border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
        margin-bottom: 0;
      }

      .tab button {
        background-color: inherit;
        float: left;
        border: none;
        outline: none;
        cursor: pointer;
        padding: var(--spacing-lg) var(--spacing-xl);
        transition: var(--transition);
        font-size: 1rem;
        font-weight: 500;
        color: var(--text-dark);
      }

      .tab button:hover {
        background-color: rgba(31, 125, 83, 0.1);
      }

      .tab button.active {
        background-color: var(--primary-color);
        color: var(--white);
      }

      /* Tab content styling */
      .tabcontent {
        display: none;
        padding: var(--spacing-xl);
        background: var(--white);
        border: 1px solid var(--border-color);
        border-top: none;
        border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
        box-shadow: var(--shadow-md);
      }

      .tabcontent.active {
        display: block;
      }

      .tabcontent h2 {
        color: var(--primary-color);
        margin-bottom: var(--spacing-lg);
        font-size: 1.5rem;
      }

      /* Table improvements */
      .table-responsive {
        overflow-x: auto;
        margin: var(--spacing-lg) 0;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-md);
        background: var(--white);
      }

      .data-table {
        width: 100%;
        min-width: 800px;
        border-collapse: collapse;
      }

      .data-table th,
      .data-table td {
        padding: var(--spacing-md);
        text-align: left;
        border-bottom: 1px solid var(--border-color);
      }

      .data-table th {
        background: var(--primary-color);
        color: var(--white);
        font-weight: 600;
        position: sticky;
        top: 0;
        z-index: 10;
      }

      .data-table tr:nth-child(even) {
        background: rgba(31, 125, 83, 0.05);
      }

      .data-table tr:hover {
        background: rgba(31, 125, 83, 0.1);
      }

      /* Action buttons */
      .action-buttons {
        display: flex;
        gap: var(--spacing-sm);
        flex-wrap: wrap;
      }

      .edit-btn,
      .delete-btn,
      .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        border: none;
        border-radius: var(--border-radius);
        cursor: pointer;
        font-size: 0.9rem;
        transition: var(--transition);
        text-decoration: none;
        display: inline-block;
        text-align: center;
      }

      .edit-btn {
        background: var(--warning-color);
        color: var(--text-dark);
      }

      .edit-btn:hover {
        background: #e0a800;
        text-decoration: none;
      }

      .delete-btn {
        background: var(--error-color);
        color: var(--white);
      }

      .delete-btn:hover {
        background: #c82333;
      }

      .btn {
        background: var(--primary-color);
        color: var(--white);
      }

      .btn:hover {
        background: var(--secondary-color);
        text-decoration: none;
      }

      /* Responsive design */
      @media (max-width: 768px) {
        .manage-container {
          max-width: 98% !important;
          width: 98% !important;
          padding: var(--spacing-md) !important;
        }

        .tab button {
          padding: var(--spacing-md);
          font-size: 0.9rem;
        }

        .action-buttons {
          flex-direction: column;
        }
      }
      .action-buttons {
        display: flex;
        gap: 5px;
      }
      .edit-btn {
        display: inline-block;
        padding: 5px 10px;
        background-color: #4a6741;
        color: white;
        text-decoration: none;
        border-radius: 3px;
        font-size: 0.8rem;
      }
      .edit-btn:hover {
        background-color: #3a5331;
      }
      .delete-btn {
        padding: 5px 10px;
        background-color: #dc3545;
        color: white;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        font-size: 0.8rem;
      }
      .delete-btn:hover {
        background-color: #c82333;
      }
      .transfer-btn {
        padding: 5px 10px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        font-size: 0.8rem;
      }
      .transfer-btn:hover {
        background-color: #0069d9;
      }
      .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
      }
      .modal-content {
        background-color: #fff;
        margin: 10% auto;
        padding: 20px;
        border-radius: 8px;
        width: 80%;
        max-width: 500px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }
      .close {
        color: #aaa;
        float: right;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
      }
      .close:hover {
        color: #000;
      }
      .tab {
        overflow: hidden;
        border: 1px solid #ccc;
        background-color: #f1f1f1;
        margin-bottom: 20px;
      }
      .tab button {
        background-color: inherit;
        float: left;
        border: none;
        outline: none;
        cursor: pointer;
        padding: 14px 16px;
        transition: 0.3s;
        font-size: 17px;
      }
      .tab button:hover {
        background-color: #ddd;
      }
      .tab button.active {
        background-color: #4a6741;
        color: white;
      }
      .tabcontent {
        display: none;
        padding: 6px 12px;
        border: 1px solid #ccc;
        border-top: none;
      }
      .filter-section {
        margin-bottom: 20px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 5px;
      }
      .filter-section select {
        margin-right: 10px;
        padding: 5px;
      }
    </style>
  </head>
  <body>
    <div class="manage-container">
        <header class="page-header">
          <h1>Manage Teacher Assignments</h1>
          <div class="nav-links">
            <a href="{{ url_for('classteacher.teacher_management_hub') }}"
              >Teacher Hub</a
            >
            |
            <a href="{{ url_for('classteacher.dashboard') }}"
              >Dashboard</a
            >
            |
            <a href="{{ url_for('auth.logout_route') }}">Logout</a>
          </div>
        </header>

        <!-- Message container for notifications -->
        <div id="message-container">
          {% if error_message %}
          <div class="message message-error">{{ error_message }}</div>
          {% endif %} {% if success_message %}
          <div class="message message-success">{{ success_message }}</div>
          {% endif %} {% if session.get('assignment_success') %}
          <div class="message message-success">
            {{ session.get('assignment_message') }}
          </div>
          {% endif %}
        </div>

        <!-- Tab navigation -->
        <div class="tab">
          <button
            class="tablinks active"
            onclick="openTab(event, 'ClassTeachers')"
          >
            Class Teacher Assignments
          </button>
          <button class="tablinks" onclick="openTab(event, 'SubjectTeachers')">
            Subject Teacher Assignments
          </button>
          <button class="tablinks" onclick="openTab(event, 'BulkTransfer')">
            Bulk Transfer
          </button>
        </div>

        <!-- Class Teacher Assignments Tab -->
        <div id="ClassTeachers" class="tabcontent" style="display: block">
          <div class="filter-section">
            <h3>Filter Class Teacher Assignments</h3>
            <select id="educationLevelFilter" onchange="filterClassTeachers()">
              <option value="">All Education Levels</option>
              <option value="lower_primary">Lower Primary (Grades 1-3)</option>
              <option value="upper_primary">Upper Primary (Grades 4-6)</option>
              <option value="junior_secondary">
                Junior Secondary (Grades 7-9)
              </option>
            </select>
            <select id="gradeFilter" onchange="filterClassTeachers()">
              <option value="">All Grades</option>
              {% for grade in grades %}
              <option value="{{ grade.id }}">{{ grade.name }}</option>
              {% endfor %}
            </select>
          </div>

          <table class="data-table">
            <thead>
              <tr>
                <th>Grade</th>
                <th>Stream</th>
                <th>Current Class Teacher</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody id="classTeacherTableBody">
              {% for assignment in class_teacher_assignments %}
              <tr
                data-grade="{{ assignment.grade_id }}"
                data-education-level="{{ assignment.education_level }}"
              >
                <td>{{ assignment.grade_level }}</td>
                <td>
                  {{ assignment.stream_name if assignment.stream_name else 'All
                  Streams' }}
                </td>
                <td>{{ assignment.teacher_username }}</td>
                <td>
                  <div class="action-buttons">
                    <button
                      class="edit-btn"
                      onclick="openReassignModal('{{ assignment.id }}', '{{ assignment.grade_level }}', '{{ assignment.stream_name if assignment.stream_name else 'All Streams' }}', '{{ assignment.teacher_username }}')"
                    >
                      Reassign
                    </button>
                    <a
                      href="{{ url_for('classteacher.manage_teacher_subjects', teacher_id=assignment.teacher_id) }}"
                      class="edit-btn"
                    >
                      Manage Subjects
                    </a>
                  </div>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>

        <!-- Subject Teacher Assignments Tab -->
        <div id="SubjectTeachers" class="tabcontent">
          <div class="filter-section">
            <h3>Filter Subject Teacher Assignments</h3>
            <select
              id="subjectEducationLevelFilter"
              onchange="filterSubjectTeachers()"
            >
              <option value="">All Education Levels</option>
              <option value="lower_primary">Lower Primary (Grades 1-3)</option>
              <option value="upper_primary">Upper Primary (Grades 4-6)</option>
              <option value="junior_secondary">
                Junior Secondary (Grades 7-9)
              </option>
            </select>
            <select id="subjectGradeFilter" onchange="filterSubjectTeachers()">
              <option value="">All Grades</option>
              {% for grade in grades %}
              <option value="{{ grade.id }}">{{ grade.name }}</option>
              {% endfor %}
            </select>
            <select id="subjectFilter" onchange="filterSubjectTeachers()">
              <option value="">All Subjects</option>
              {% for subject in subjects %}
              <option value="{{ subject.id }}">{{ subject.name }}</option>
              {% endfor %}
            </select>
          </div>

          <table class="data-table">
            <thead>
              <tr>
                <th>Subject</th>
                <th>Grade</th>
                <th>Stream</th>
                <th>Current Teacher</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody id="subjectTeacherTableBody">
              {% for assignment in subject_assignments %}
              <tr
                data-grade="{{ assignment.grade_id }}"
                data-subject="{{ assignment.subject_id }}"
                data-education-level="{{ assignment.education_level }}"
              >
                <td>{{ assignment.subject_name }}</td>
                <td>{{ assignment.grade_level }}</td>
                <td>
                  {{ assignment.stream_name if assignment.stream_name else 'All
                  Streams' }}
                </td>
                <td>{{ assignment.teacher_username }}</td>
                <td>
                  <div class="action-buttons">
                    <button
                      class="edit-btn"
                      onclick="openReassignSubjectModal('{{ assignment.id }}', '{{ assignment.subject_name }}', '{{ assignment.grade_level }}', '{{ assignment.stream_name if assignment.stream_name else 'All Streams' }}', '{{ assignment.teacher_username }}')"
                    >
                      Reassign
                    </button>
                    <a
                      href="{{ url_for('classteacher.manage_teacher_subjects', teacher_id=assignment.teacher_id) }}"
                      class="edit-btn"
                    >
                      Manage Subjects
                    </a>
                  </div>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>

        <!-- Bulk Transfer Tab -->
        <div id="BulkTransfer" class="tabcontent">
          <h2>Bulk Transfer Assignments</h2>
          <p>
            Use this form to transfer all assignments from one teacher to
            another.
          </p>

          <form
            method="POST"
            action="{{ url_for('classteacher.bulk_transfer_assignments') }}"
          >
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />

            <div class="form-group">
              <label for="from_teacher_id">Transfer From:</label>
              <select
                id="from_teacher_id"
                name="from_teacher_id"
                required
                class="form-control"
              >
                <option value="">-- Select Teacher --</option>
                {% for teacher in teachers %}
                <option value="{{ teacher.id }}">{{ teacher.username }}</option>
                {% endfor %}
              </select>
            </div>

            <div class="form-group">
              <label for="to_teacher_id">Transfer To:</label>
              <select
                id="to_teacher_id"
                name="to_teacher_id"
                required
                class="form-control"
              >
                <option value="">-- Select Teacher --</option>
                {% for teacher in teachers %}
                <option value="{{ teacher.id }}">{{ teacher.username }}</option>
                {% endfor %}
              </select>
            </div>

            <div class="form-group">
              <label>
                <input
                  type="checkbox"
                  name="transfer_class_teacher"
                  value="1"
                />
                Transfer Class Teacher Assignments
              </label>
            </div>

            <div class="form-group">
              <label>
                <input
                  type="checkbox"
                  name="transfer_subject_assignments"
                  value="1"
                />
                Transfer Subject Assignments
              </label>
            </div>

            <button
              type="submit"
              name="bulk_transfer"
              class="btn btn-primary"
              onclick="return confirm('Are you sure you want to transfer all selected assignments? This action cannot be undone.')"
            >
              Transfer Assignments
            </button>
          </form>
        </div>

        <!-- Reassign Class Teacher Modal -->
        <div id="reassignModal" class="modal">
          <div class="modal-content">
            <span class="close" onclick="closeReassignModal()">&times;</span>
            <h2>Reassign Class Teacher</h2>
            <form
              id="reassignForm"
              method="POST"
              action="{{ url_for('classteacher.reassign_class_teacher') }}"
            >
              <input
                type="hidden"
                name="csrf_token"
                value="{{ csrf_token() }}"
              />
              <input type="hidden" id="assignment_id" name="assignment_id" />

              <div class="form-group">
                <label for="current_details">Current Assignment:</label>
                <input
                  type="text"
                  id="current_details"
                  class="form-control"
                  readonly
                />
              </div>

              <div class="form-group">
                <label for="new_teacher_id">New Class Teacher:</label>
                <select
                  id="new_teacher_id"
                  name="new_teacher_id"
                  required
                  class="form-control"
                >
                  <option value="">-- Select Teacher --</option>
                  {% for teacher in teachers %}
                  <option value="{{ teacher.id }}">
                    {{ teacher.username }}
                  </option>
                  {% endfor %}
                </select>
              </div>

              <button
                type="submit"
                name="reassign_class_teacher"
                class="btn btn-primary"
              >
                Reassign Class Teacher
              </button>
            </form>
          </div>
        </div>

        <!-- Reassign Subject Teacher Modal -->
        <div id="reassignSubjectModal" class="modal">
          <div class="modal-content">
            <span class="close" onclick="closeReassignSubjectModal()"
              >&times;</span
            >
            <h2>Reassign Subject Teacher</h2>
            <form
              id="reassignSubjectForm"
              method="POST"
              action="{{ url_for('classteacher.reassign_subject_teacher') }}"
            >
              <input
                type="hidden"
                name="csrf_token"
                value="{{ csrf_token() }}"
              />
              <input
                type="hidden"
                id="subject_assignment_id"
                name="assignment_id"
              />

              <div class="form-group">
                <label for="subject_current_details">Current Assignment:</label>
                <input
                  type="text"
                  id="subject_current_details"
                  class="form-control"
                  readonly
                />
              </div>

              <div class="form-group">
                <label for="subject_new_teacher_id">New Subject Teacher:</label>
                <select
                  id="subject_new_teacher_id"
                  name="new_teacher_id"
                  required
                  class="form-control"
                >
                  <option value="">-- Select Teacher --</option>
                  {% for teacher in teachers %}
                  <option value="{{ teacher.id }}">
                    {{ teacher.username }}
                  </option>
                  {% endfor %}
                </select>
              </div>

              <button
                type="submit"
                name="reassign_subject_teacher"
                class="btn btn-primary"
              >
                Reassign Subject Teacher
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>

    <script>
      // Auto-hide messages after 5 seconds
      document.addEventListener("DOMContentLoaded", function () {
        setTimeout(function () {
          const messages = document.querySelectorAll(".message");
          messages.forEach((message) => {
            message.style.display = "none";
          });
        }, 5000);
      });

      // Tab functionality
      function openTab(evt, tabName) {
        var i, tabcontent, tablinks;
        tabcontent = document.getElementsByClassName("tabcontent");
        for (i = 0; i < tabcontent.length; i++) {
          tabcontent[i].style.display = "none";
        }
        tablinks = document.getElementsByClassName("tablinks");
        for (i = 0; i < tablinks.length; i++) {
          tablinks[i].className = tablinks[i].className.replace(" active", "");
        }
        document.getElementById(tabName).style.display = "block";
        evt.currentTarget.className += " active";
      }

      // Filter class teachers
      function filterClassTeachers() {
        const educationLevel = document.getElementById(
          "educationLevelFilter"
        ).value;
        const gradeId = document.getElementById("gradeFilter").value;
        const rows = document.querySelectorAll("#classTeacherTableBody tr");

        rows.forEach((row) => {
          let showRow = true;

          if (educationLevel && row.dataset.educationLevel !== educationLevel) {
            showRow = false;
          }

          if (gradeId && row.dataset.grade !== gradeId) {
            showRow = false;
          }

          row.style.display = showRow ? "" : "none";
        });
      }

      // Filter subject teachers
      function filterSubjectTeachers() {
        const educationLevel = document.getElementById(
          "subjectEducationLevelFilter"
        ).value;
        const gradeId = document.getElementById("subjectGradeFilter").value;
        const subjectId = document.getElementById("subjectFilter").value;
        const rows = document.querySelectorAll("#subjectTeacherTableBody tr");

        rows.forEach((row) => {
          let showRow = true;

          if (educationLevel && row.dataset.educationLevel !== educationLevel) {
            showRow = false;
          }

          if (gradeId && row.dataset.grade !== gradeId) {
            showRow = false;
          }

          if (subjectId && row.dataset.subject !== subjectId) {
            showRow = false;
          }

          row.style.display = showRow ? "" : "none";
        });
      }

      // Reassign class teacher modal functions
      function openReassignModal(assignmentId, grade, stream, teacher) {
        document.getElementById("assignment_id").value = assignmentId;
        document.getElementById(
          "current_details"
        ).value = `${grade} ${stream} - Current teacher: ${teacher}`;
        document.getElementById("reassignModal").style.display = "block";
      }

      function closeReassignModal() {
        document.getElementById("reassignModal").style.display = "none";
      }

      // Reassign subject teacher modal functions
      function openReassignSubjectModal(
        assignmentId,
        subject,
        grade,
        stream,
        teacher
      ) {
        document.getElementById("subject_assignment_id").value = assignmentId;
        document.getElementById(
          "subject_current_details"
        ).value = `${subject} - ${grade} ${stream} - Current teacher: ${teacher}`;
        document.getElementById("reassignSubjectModal").style.display = "block";
      }

      function closeReassignSubjectModal() {
        document.getElementById("reassignSubjectModal").style.display = "none";
      }

      // Close modals when clicking outside
      window.onclick = function (event) {
        if (event.target == document.getElementById("reassignModal")) {
          closeReassignModal();
        }
        if (event.target == document.getElementById("reassignSubjectModal")) {
          closeReassignSubjectModal();
        }
      };
    </script>
  </body>
</html>
