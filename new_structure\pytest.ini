[tool:pytest]
testpaths = testing_framework
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=new_structure
    --cov-report=html:testing_framework/reports/coverage
    --cov-report=term-missing
    --html=testing_framework/reports/pytest_report.html
    --self-contained-html
    --maxfail=5
    --timeout=300
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    security: Security tests
    performance: Performance tests
    slow: Slow running tests
    database: Database tests
    api: API tests
    frontend: Frontend tests
    headteacher: Headteacher role tests
    classteacher: Classteacher role tests
    teacher: Teacher role tests
    parent: Parent portal tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
