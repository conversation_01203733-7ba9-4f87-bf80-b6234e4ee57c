<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Student Reports - Kirima Primary School</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/style.css') }}"
    />
    <style>
      body {
        font-family: "Poppins", sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f4f4f4;
        color: #333;
      }
      .container {
        max-width: 1200px;
        margin: 0 auto;
        background-color: white;
        padding: 30px;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
      }
      .header {
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 15px;
        border-bottom: 2px solid #eee;
      }
      .header h1 {
        margin: 0;
        font-size: 28px;
        color: #2c3e50;
      }
      .header p {
        margin: 10px 0 0;
        font-size: 16px;
        color: #7f8c8d;
      }
      .action-buttons {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
      }
      .back-btn,
      .class-report-btn,
      .download-all-btn {
        display: inline-block;
        padding: 10px 20px;
        color: white;
        text-decoration: none;
        border-radius: 5px;
        transition: background-color 0.3s;
        font-weight: 500;
        border: none;
        cursor: pointer;
      }
      .back-btn {
        background-color: #3498db;
      }
      .back-btn:hover {
        background-color: #2980b9;
      }
      .class-report-btn {
        background-color: #f39c12;
      }
      .class-report-btn:hover {
        background-color: #d35400;
      }
      .download-all-btn {
        background-color: #27ae60;
      }
      .download-all-btn:hover {
        background-color: #219653;
      }
      .students-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
      }
      .students-table th,
      .students-table td {
        border: 1px solid #ddd;
        padding: 12px;
        text-align: left;
      }
      .students-table th {
        background-color: #3498db;
        color: white;
        font-weight: 600;
      }
      .students-table tr:nth-child(even) {
        background-color: #f9f9f9;
      }
      .students-table tr:hover {
        background-color: #f1f1f1;
      }
      .view-btn,
      .print-btn {
        display: inline-block;
        padding: 8px 15px;
        margin-right: 5px;
        color: white;
        text-decoration: none;
        border-radius: 4px;
        font-size: 14px;
        transition: background-color 0.3s;
      }
      .view-btn {
        background-color: #3498db;
      }
      .view-btn:hover {
        background-color: #2980b9;
      }
      .print-btn {
        background-color: #27ae60;
      }
      .print-btn:hover {
        background-color: #219653;
      }
      .info-box {
        background-color: #d1ecf1;
        color: #0c5460;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        border-left: 5px solid #0c5460;
      }
      .info-box h3 {
        margin-top: 0;
        font-size: 18px;
      }
      .info-box p {
        margin-bottom: 0;
      }
      .pagination {
        display: flex;
        justify-content: center;
        margin-top: 20px;
      }
      .pagination a {
        color: #3498db;
        padding: 8px 16px;
        text-decoration: none;
        transition: background-color 0.3s;
        border: 1px solid #ddd;
        margin: 0 4px;
      }
      .pagination a.active {
        background-color: #3498db;
        color: white;
        border: 1px solid #3498db;
      }
      .pagination a:hover:not(.active) {
        background-color: #ddd;
      }
      .search-box {
        margin-bottom: 20px;
      }
      .search-box input {
        padding: 10px;
        width: 300px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 14px;
      }
      .search-box button {
        padding: 10px 15px;
        background-color: #3498db;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
      }
      .search-box button:hover {
        background-color: #2980b9;
      }
      .no-students {
        text-align: center;
        padding: 20px;
        background-color: #f8d7da;
        color: #721c24;
        border-radius: 5px;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>Individual Student Reports</h1>
        <p>
          Grade {{ grade }} {{ stream }} - {{ term.replace('_', ' ').upper() }} -
          {{ assessment_type.upper() }}
        </p>
      </div>

      <div class="action-buttons">
        <a href="{{ url_for('classteacher.dashboard') }}" class="back-btn">
          Back to Dashboard
        </a>
        <a
          href="{{ url_for('classteacher.preview_class_report', grade=grade, stream=stream, term=term, assessment_type=assessment_type) }}"
          class="class-report-btn"
        >
          View Class Report
        </a>
        <button
          onclick="downloadAllIndividualReports('{{ grade }}', '{{ stream }}', '{{ term }}', '{{ assessment_type }}')"
          class="download-all-btn"
          id="downloadAllBtn"
        >
          Download All Reports (ZIP)
        </button>
      </div>

      <div class="info-box">
        <h3>Individual Reports</h3>
        <p>
          View and print individual student reports. You can also download all
          reports as a ZIP file.
        </p>
      </div>

      <div class="search-box">
        <form method="GET">
          <input
            type="text"
            name="search"
            placeholder="Search by student name..."
            value="{{ search_query }}"
          />
          <button type="submit">Search</button>
          {% if search_query %}
          <a
            href="{{ url_for('classteacher.view_student_reports', grade=grade, stream=stream, term=term, assessment_type=assessment_type) }}"
            style="margin-left: 10px"
            >Clear</a
          >
          {% endif %}
        </form>
      </div>

      {% if students %}
      <table class="students-table">
        <thead>
          <tr>
            <th style="width: 5%">#</th>
            <th style="width: 40%">Student Name</th>
            <th style="width: 15%">Admission No.</th>
            <th style="width: 15%">Average</th>
            <th style="width: 25%">Actions</th>
          </tr>
        </thead>
        <tbody>
          {% for student in students %}
          <tr>
            <td>{{ loop.index + (page-1) * per_page }}</td>
            <td>{{ student.name }}</td>
            <td>{{ student.admission_number }}</td>
            <td>
              {% if student.average is defined %}
              {{ student.average|round(1) }}%
              {% else %}
              N/A
              {% endif %}
            </td>
            <td>
              <a
                href="{{ url_for('classteacher.preview_individual_report', grade=grade, stream=stream, term=term, assessment_type=assessment_type, student_name=student.name) }}"
                class="view-btn"
              >
                View Report
              </a>
              <a
                href="{{ url_for('classteacher.print_individual_report', grade=grade, stream=stream, term=term, assessment_type=assessment_type, student_name=student.name) }}"
                class="print-btn"
                target="_blank"
              >
                Print Report
              </a>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>

      <!-- Pagination -->
      {% if total_pages > 1 %}
      <div class="pagination">
        {% if page > 1 %}
        <a
          href="{{ url_for('classteacher.view_student_reports', grade=grade, stream=stream, term=term, assessment_type=assessment_type, page=page-1, search=search_query) }}"
          >&laquo; Previous</a
        >
        {% endif %} {% for p in range(1, total_pages + 1) %}
        <a
          href="{{ url_for('classteacher.view_student_reports', grade=grade, stream=stream, term=term, assessment_type=assessment_type, page=p, search=search_query) }}"
          {% if p==page %}class="active"{% endif %}
          >{{ p }}</a
        >
        {% endfor %} {% if page < total_pages %}
        <a
          href="{{ url_for('classteacher.view_student_reports', grade=grade, stream=stream, term=term, assessment_type=assessment_type, page=page+1, search=search_query) }}"
          >Next &raquo;</a
        >
        {% endif %}
      </div>
      {% endif %} {% else %}
      <div class="no-students">
        <p>No students found for this class or matching your search criteria.</p>
      </div>
      {% endif %}
    </div>

    <script>
        // Function to download all individual reports as ZIP
        async function downloadAllIndividualReports(grade, stream, term, assessmentType) {
            const downloadBtn = document.getElementById('downloadAllBtn');
            const originalText = downloadBtn.innerHTML;

            try {
                // Show loading state
                downloadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating ZIP...';
                downloadBtn.disabled = true;

                // Make the request
                const response = await fetch(`/classteacher/generate_all_individual_reports/${encodeURIComponent(grade)}/${encodeURIComponent(stream)}/${encodeURIComponent(term)}/${encodeURIComponent(assessmentType)}`, {
                    method: 'GET',
                    credentials: 'same-origin'
                });

                if (!response.ok) {
                    // If response is not ok, try to get error message
                    const errorText = await response.text();
                    throw new Error(`Server error: ${response.status} - ${errorText}`);
                }

                // Check if response is actually a ZIP file
                const contentType = response.headers.get('Content-Type');
                if (!contentType || !contentType.includes('application/zip')) {
                    // This might be a redirect response (HTML), show appropriate message
                    throw new Error('No reports could be generated. Please ensure students have marks for the selected term and assessment type.');
                }

                // Get filename from response headers
                const contentDisposition = response.headers.get('Content-Disposition');
                let filename = `Individual_Reports_${grade.replace(' ', '_')}_${stream}_${term}_${assessmentType}_${new Date().toISOString().slice(0, 10)}.zip`;

                if (contentDisposition) {
                    const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                    if (filenameMatch) {
                        filename = filenameMatch[1];
                    }
                }

                // Download the file
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                // Show success message
                showNotification('ZIP file downloaded successfully!', 'success');

            } catch (error) {
                console.error('Download error:', error);
                showNotification(error.message || 'Error downloading ZIP file. Please try again.', 'error');
            } finally {
                // Restore button state
                downloadBtn.innerHTML = originalText;
                downloadBtn.disabled = false;
            }
        }

        // Notification function
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
                color: white;
                padding: 15px 20px;
                border-radius: 5px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                z-index: 10000;
                max-width: 400px;
                word-wrap: break-word;
            `;
            notification.textContent = message;

            // Add close button
            const closeBtn = document.createElement('button');
            closeBtn.innerHTML = '×';
            closeBtn.style.cssText = `
                background: none;
                border: none;
                color: white;
                font-size: 20px;
                font-weight: bold;
                margin-left: 10px;
                cursor: pointer;
                padding: 0;
                line-height: 1;
            `;
            closeBtn.onclick = () => notification.remove();
            notification.appendChild(closeBtn);

            // Add to page
            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    </script>
  </body>
</html>
