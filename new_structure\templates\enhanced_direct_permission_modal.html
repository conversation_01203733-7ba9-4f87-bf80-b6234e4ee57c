<!-- Enhanced Direct Permission Granting Modal -->
<div id="directGrantModal" class="modal-overlay" style="display: none;">
    <div class="modal-container" style="max-width: 600px;">
        <div class="modal-header">
            <h3><i class="fas fa-user-plus"></i> Grant Direct Permission</h3>
            <button type="button" class="modal-close" onclick="closeDirectGrantModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="modal-body">
            <form id="directGrantForm">
                <!-- Teacher Selection -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-user"></i> Select Teacher
                    </label>
                    <select id="directGrantTeacher" class="form-select" required>
                        <option value="">Choose a teacher...</option>
                        <!-- Teachers will be populated by JavaScript -->
                    </select>
                </div>

                <!-- Class Selection -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-school"></i> Select Class/Stream
                    </label>
                    <div class="modern-grid grid-cols-2">
                        <div>
                            <label class="form-label" style="font-size: 0.9rem;">Grade</label>
                            <select id="directGrantGrade" class="form-select" required onchange="loadStreamsForDirectGrant()">
                                <option value="">Choose grade...</option>
                                <!-- Grades will be populated by JavaScript -->
                            </select>
                        </div>
                        <div>
                            <label class="form-label" style="font-size: 0.9rem;">Stream (Optional)</label>
                            <select id="directGrantStream" class="form-select">
                                <option value="">All streams</option>
                                <!-- Streams will be populated by JavaScript -->
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Duration Selection -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-clock"></i> Permission Duration
                    </label>
                    <select id="directGrantDuration" class="form-select" required onchange="updateExpirationPreview()">
                        <option value="">Choose duration...</option>
                        <!-- Duration options will be populated by JavaScript -->
                    </select>
                    <div id="expirationPreview" class="form-help" style="margin-top: var(--space-2); display: none;">
                        <i class="fas fa-info-circle"></i> <span id="expirationText"></span>
                    </div>
                </div>

                <!-- Notes -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-sticky-note"></i> Notes (Optional)
                    </label>
                    <textarea id="directGrantNotes" class="form-textarea" rows="3" 
                              placeholder="Add any notes about this permission grant..."></textarea>
                </div>

                <!-- Permission Preview -->
                <div id="permissionPreview" class="alert alert-info" style="display: none;">
                    <h4><i class="fas fa-eye"></i> Permission Preview</h4>
                    <div id="previewContent"></div>
                </div>
            </form>
        </div>
        
        <div class="modal-footer">
            <button type="button" class="modern-btn btn-outline" onclick="closeDirectGrantModal()">
                <i class="fas fa-times"></i> Cancel
            </button>
            <button type="button" class="modern-btn btn-primary" onclick="submitDirectGrant()">
                <i class="fas fa-check"></i> Grant Permission
            </button>
        </div>
    </div>
</div>

<!-- Bulk Direct Grant Modal -->
<div id="bulkDirectGrantModal" class="modal-overlay" style="display: none;">
    <div class="modal-container" style="max-width: 700px;">
        <div class="modal-header">
            <h3><i class="fas fa-users"></i> Bulk Grant Permissions</h3>
            <button type="button" class="modal-close" onclick="closeBulkDirectGrantModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="modal-body">
            <form id="bulkDirectGrantForm">
                <!-- Teacher Selection -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-user"></i> Select Teacher
                    </label>
                    <select id="bulkGrantTeacher" class="form-select" required>
                        <option value="">Choose a teacher...</option>
                        <!-- Teachers will be populated by JavaScript -->
                    </select>
                </div>

                <!-- Multiple Class Selection -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-school"></i> Select Classes/Streams
                    </label>
                    <div id="classSelectionContainer" style="max-height: 200px; overflow-y: auto; border: 1px solid var(--gray-300); border-radius: var(--radius-md); padding: var(--space-3);">
                        <!-- Class checkboxes will be populated by JavaScript -->
                    </div>
                    <div class="form-help">
                        <i class="fas fa-info-circle"></i> Select multiple classes to grant permissions for
                    </div>
                </div>

                <!-- Duration Selection -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-clock"></i> Permission Duration
                    </label>
                    <select id="bulkGrantDuration" class="form-select" required onchange="updateBulkExpirationPreview()">
                        <option value="">Choose duration...</option>
                        <!-- Duration options will be populated by JavaScript -->
                    </select>
                    <div id="bulkExpirationPreview" class="form-help" style="margin-top: var(--space-2); display: none;">
                        <i class="fas fa-info-circle"></i> <span id="bulkExpirationText"></span>
                    </div>
                </div>

                <!-- Notes -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-sticky-note"></i> Notes (Optional)
                    </label>
                    <textarea id="bulkGrantNotes" class="form-textarea" rows="3" 
                              placeholder="Add any notes about these permission grants..."></textarea>
                </div>

                <!-- Selected Classes Preview -->
                <div id="selectedClassesPreview" class="alert alert-info" style="display: none;">
                    <h4><i class="fas fa-list"></i> Selected Classes</h4>
                    <div id="selectedClassesList"></div>
                </div>
            </form>
        </div>
        
        <div class="modal-footer">
            <button type="button" class="modern-btn btn-outline" onclick="closeBulkDirectGrantModal()">
                <i class="fas fa-times"></i> Cancel
            </button>
            <button type="button" class="modern-btn btn-primary" onclick="submitBulkDirectGrant()">
                <i class="fas fa-check"></i> Grant All Permissions
            </button>
        </div>
    </div>
</div>

<!-- Extend Permission Modal -->
<div id="extendPermissionModal" class="modal-overlay" style="display: none;">
    <div class="modal-container" style="max-width: 500px;">
        <div class="modal-header">
            <h3><i class="fas fa-clock"></i> Extend Permission</h3>
            <button type="button" class="modal-close" onclick="closeExtendPermissionModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="modal-body">
            <form id="extendPermissionForm">
                <input type="hidden" id="extendPermissionId">
                
                <!-- Current Permission Info -->
                <div id="currentPermissionInfo" class="alert alert-info">
                    <!-- Permission details will be populated by JavaScript -->
                </div>

                <!-- Extension Duration -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-plus-circle"></i> Extend By
                    </label>
                    <select id="extendDuration" class="form-select" required onchange="updateExtensionPreview()">
                        <option value="">Choose extension duration...</option>
                        <!-- Duration options will be populated by JavaScript -->
                    </select>
                    <div id="extensionPreview" class="form-help" style="margin-top: var(--space-2); display: none;">
                        <i class="fas fa-info-circle"></i> <span id="extensionText"></span>
                    </div>
                </div>
            </form>
        </div>
        
        <div class="modal-footer">
            <button type="button" class="modern-btn btn-outline" onclick="closeExtendPermissionModal()">
                <i class="fas fa-times"></i> Cancel
            </button>
            <button type="button" class="modern-btn btn-primary" onclick="submitExtendPermission()">
                <i class="fas fa-clock"></i> Extend Permission
            </button>
        </div>
    </div>
</div>

<style>
/* Enhanced modal styles for permission management */
.permission-preview-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-2);
    background: var(--gray-50);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-2);
}

.class-checkbox-item {
    display: flex;
    align-items: center;
    padding: var(--space-2);
    border-bottom: 1px solid var(--gray-200);
}

.class-checkbox-item:last-child {
    border-bottom: none;
}

.class-checkbox-item input[type="checkbox"] {
    margin-right: var(--space-2);
}

.duration-badge {
    display: inline-block;
    padding: var(--space-1) var(--space-2);
    background: var(--blue-100);
    color: var(--blue-800);
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
}

.expiration-warning {
    color: var(--orange-600);
    font-weight: 500;
}

.expiration-permanent {
    color: var(--green-600);
    font-weight: 500;
}
</style>
