<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Teacher Assignments - Hillview School</title>
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/style.css') }}"
    />
    <style>
      /* Override container styles for manage pages */
      .manage-container {
        max-width: 95% !important;
        width: 95% !important;
        margin: 80px auto 60px !important;
        padding: var(--spacing-xl) !important;
      }

      /* Page header styling */
      .page-header {
        background: var(--white);
        padding: var(--spacing-lg);
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
        margin-bottom: var(--spacing-xl);
      }

      .page-header h1 {
        color: var(--primary-color);
        margin-bottom: var(--spacing-sm);
        font-size: 2.5rem;
      }

      .nav-links a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
        margin-right: var(--spacing-md);
      }

      .nav-links a:hover {
        text-decoration: underline;
      }

      /* Forms grid layout */
      .forms-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-xl);
        margin: var(--spacing-xl) 0;
      }

      /* Form card styling */
      .form-card {
        background: var(--white);
        padding: var(--spacing-lg);
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
      }

      .form-card h2 {
        color: var(--primary-color);
        margin-bottom: var(--spacing-lg);
        font-size: 1.3rem;
      }

      /* Education level sections */
      .education-level-section {
        margin-bottom: var(--spacing-xl);
        padding: var(--spacing-lg);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius-lg);
        background: rgba(31, 125, 83, 0.05);
      }

      .education-level-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-md);
      }

      .education-level-header h3 {
        color: var(--primary-color);
        margin: 0;
      }

      .add-level-btn {
        background: var(--primary-color);
        color: var(--white);
        border: none;
        padding: var(--spacing-md) var(--spacing-lg);
        border-radius: var(--border-radius);
        cursor: pointer;
        transition: var(--transition);
        font-weight: 500;
      }

      .add-level-btn:hover {
        background: var(--secondary-color);
      }

      .remove-level-btn {
        background: var(--error-color);
        color: var(--white);
        border: none;
        padding: var(--spacing-sm) var(--spacing-md);
        border-radius: var(--border-radius);
        cursor: pointer;
        font-size: 0.9rem;
        transition: var(--transition);
      }

      .remove-level-btn:hover {
        background: #c82333;
      }

      .assignment-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-lg);
      }

      .assignment-section {
        margin-bottom: var(--spacing-lg);
      }

      .subject-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-md);
      }

      .class-teacher-option {
        margin-top: var(--spacing-lg);
        padding-top: var(--spacing-md);
        border-top: 1px solid var(--border-color);
      }

      .template-section {
        display: none;
      }

      .assignment-summary {
        margin-top: var(--spacing-xl);
        padding: var(--spacing-lg);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius-lg);
        background: rgba(31, 125, 83, 0.1);
      }

      .assignment-summary h3 {
        color: var(--primary-color);
        margin-bottom: var(--spacing-md);
      }

      .summary-item {
        margin-bottom: var(--spacing-sm);
        padding: var(--spacing-sm);
        border-bottom: 1px solid var(--border-color);
      }

      /* Form styling */
      .form-group {
        margin-bottom: var(--spacing-lg);
      }

      .form-group label {
        display: block;
        margin-bottom: var(--spacing-sm);
        color: var(--text-dark);
        font-weight: 500;
      }

      .form-control {
        width: 100%;
        padding: var(--spacing-md);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        font-size: 1rem;
        color: var(--text-dark);
        background: var(--white);
      }

      .form-control:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(31, 125, 83, 0.1);
      }

      /* Button styling */
      .manage-btn {
        background: var(--primary-color) !important;
        color: var(--white);
        border: none;
        padding: var(--spacing-md) var(--spacing-lg);
        border-radius: var(--border-radius);
        cursor: pointer;
        transition: var(--transition);
        font-size: 1rem;
        font-weight: 500;
      }

      .manage-btn:hover {
        background: var(--secondary-color) !important;
      }

      /* Message styling */
      .message {
        padding: var(--spacing-md);
        border-radius: var(--border-radius);
        margin-bottom: var(--spacing-lg);
      }

      .message-error {
        background: rgba(220, 53, 69, 0.1);
        border: 1px solid var(--error-color);
        color: var(--error-color);
      }

      .message-success {
        background: rgba(31, 125, 83, 0.1);
        border: 1px solid var(--primary-color);
        color: var(--primary-color);
      }

      /* Responsive design */
      @media (max-width: 768px) {
        .manage-container {
          max-width: 98% !important;
          width: 98% !important;
          padding: var(--spacing-md) !important;
        }

        .forms-grid {
          grid-template-columns: 1fr;
          gap: var(--spacing-lg);
        }

        .assignment-grid {
          grid-template-columns: 1fr;
          gap: var(--spacing-md);
        }

        .subject-grid {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>
  <body>
    <div class="manage-container">
        <header class="page-header">
          <h1>Teacher Assignments</h1>
          <div class="nav-links">
            <a href="{{ url_for('classteacher.teacher_management_hub') }}"
              >Teacher Hub</a
            >
            |
            <a href="{{ url_for('classteacher.dashboard') }}"
              >Dashboard</a
            >
            |
            <a href="{{ url_for('auth.logout_route') }}">Logout</a>
          </div>
        </header>

        <!-- Message container for notifications -->
        <div id="message-container">
          {% if error_message %}
          <div class="message message-error">{{ error_message }}</div>
          {% endif %} {% if success_message %}
          <div class="message message-success">{{ success_message }}</div>
          {% endif %}
        </div>

        <div class="forms-grid">
          <!-- Teacher Selection Form -->
          <div class="form-card">
            <h2>Bulk Subject Assignment</h2>
            <p>
              Assign multiple subjects to a teacher across different education
              levels.
            </p>

            <form
              id="bulk-assignment-form"
              method="POST"
              action="{{ url_for('bulk_assignments.bulk_assign_subjects_new') }}"
            >
              <input
                type="hidden"
                name="csrf_token"
                value="{{ csrf_token() }}"
              />

              <!-- Teacher Selection -->
              <div class="form-group">
                <label for="teacher_id">Select Teacher:</label>
                <select
                  name="teacher_id"
                  id="teacher_id"
                  class="form-control"
                  required
                >
                  <option value="">-- Select Teacher --</option>
                  {% for teacher in teachers %}
                  <option value="{{ teacher.id }}">
                    {{ teacher.username }}
                  </option>
                  {% endfor %}
                </select>
                <p style="margin-top: 5px; font-size: 0.9em; color: #666">
                  Note: To add a new teacher, use the "Add New Teacher" form on
                  the Manage Teachers page.
                </p>
              </div>

              <!-- Education Level Sections Container -->
              <div id="education-levels-container">
                <!-- First education level section (always present) -->
                <div class="education-level-section" id="level-section-1">
                  <div class="education-level-header">
                    <h3>Education Level 1</h3>
                  </div>

                  <div class="form-group">
                    <label for="education_level_1"
                      >Select Education Level:</label
                    >
                    <select
                      name="education_level_1"
                      id="education_level_1"
                      class="education-level-select form-control"
                      onchange="updateSubjects(1)"
                      required
                    >
                      <option value="">-- Select Education Level --</option>
                      <option value="lower_primary">
                        Lower Primary (Grades 1-3)
                      </option>
                      <option value="upper_primary">
                        Upper Primary (Grades 4-6)
                      </option>
                      <option value="junior_secondary">
                        Junior Secondary (Grades 7-9)
                      </option>
                    </select>
                  </div>

                  <div class="assignment-grid">
                    <div class="assignment-section">
                      <div class="form-group">
                        <label for="grade_id_1">Select Grade:</label>
                        <select
                          name="grade_id_1"
                          id="grade_id_1"
                          class="grade-select form-control"
                          onchange="updateStreams(1)"
                          required
                        >
                          <option value="">-- Select Grade --</option>
                          <!-- Will be populated based on education level -->
                        </select>
                      </div>

                      <div class="form-group">
                        <label for="stream_id_1"
                          >Select Stream (Optional):</label
                        >
                        <select
                          name="stream_id_1"
                          id="stream_id_1"
                          class="stream-select form-control"
                        >
                          <option value="">-- All Streams --</option>
                          <!-- Will be populated based on grade -->
                        </select>
                      </div>

                      <div class="class-teacher-option">
                        <label>
                          <input
                            type="checkbox"
                            name="is_class_teacher_1"
                            id="is_class_teacher_1"
                            value="1"
                          />
                          Designate as Class Teacher
                        </label>
                      </div>
                    </div>

                    <div class="assignment-section">
                      <div class="form-group">
                        <label for="subjects_1"
                          >Subjects (Hold Ctrl/Cmd to select multiple):</label
                        >
                        <select
                          name="subjects_1[]"
                          id="subjects-select-1"
                          class="form-control"
                          multiple
                          style="min-height: 150px"
                        >
                          <!-- Will be populated based on education level -->
                          <option disabled>
                            Please select an education level first
                          </option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Add Education Level Button -->
              <div style="margin: 20px 0">
                <button
                  type="button"
                  class="add-level-btn"
                  onclick="addEducationLevel()"
                >
                  + Add Another Education Level
                </button>
              </div>

              <!-- Assignment Summary -->
              <div
                class="assignment-summary"
                id="assignment-summary"
                style="display: none"
              >
                <h3>Assignment Summary</h3>
                <div id="summary-content">
                  <!-- Will be populated via JavaScript -->
                </div>
              </div>

              <!-- Submit Button -->
              <div style="margin-top: 20px">
                <button
                  type="submit"
                  class="manage-btn"
                  style="background-color: #4a6741"
                >
                  Assign Subjects
                </button>
              </div>
            </form>
          </div>

          <!-- Tips Section -->
          <div class="form-card">
            <h2>Assignment Tips</h2>
            <div class="tips-content">
              <p>Here are some tips for bulk assigning subjects to teachers:</p>
              <ul>
                <li>
                  Start by selecting the education level (Lower Primary, Upper
                  Primary, or Junior Secondary)
                </li>
                <li>Then select the grade and stream (if applicable)</li>
                <li>
                  Choose the subjects the teacher will teach at this level
                </li>
                <li>
                  You can add multiple education levels for teachers who teach
                  across different levels
                </li>
                <li>
                  Check the "Designate as Class Teacher" option if the teacher
                  is the class teacher for the selected grade/stream
                </li>
                <li>
                  Only one teacher can be designated as the class teacher for a
                  specific stream
                </li>
                <li>
                  If no stream is selected, the assignment applies to all
                  streams in the grade
                </li>
                <li>
                  You can create a new teacher directly from this form if needed
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Template for additional education level sections -->
    <div class="template-section" id="level-section-template">
      <div class="education-level-section">
        <div class="education-level-header">
          <h3>Education Level {LEVEL_NUM}</h3>
          <button
            type="button"
            class="remove-level-btn"
            onclick="removeEducationLevel({LEVEL_NUM})"
          >
            Remove
          </button>
        </div>

        <div class="form-group">
          <label for="education_level_{LEVEL_NUM}"
            >Select Education Level:</label
          >
          <select
            name="education_level_{LEVEL_NUM}"
            id="education_level_{LEVEL_NUM}"
            class="education-level-select form-control"
            onchange="updateSubjects({LEVEL_NUM})"
            required
          >
            <option value="">-- Select Education Level --</option>
            <option value="lower_primary">Lower Primary (Grades 1-3)</option>
            <option value="upper_primary">Upper Primary (Grades 4-6)</option>
            <option value="junior_secondary">
              Junior Secondary (Grades 7-9)
            </option>
          </select>
        </div>

        <div class="assignment-grid">
          <div class="assignment-section">
            <div class="form-group">
              <label for="grade_id_{LEVEL_NUM}">Select Grade:</label>
              <select
                name="grade_id_{LEVEL_NUM}"
                id="grade_id_{LEVEL_NUM}"
                class="grade-select form-control"
                onchange="updateStreams({LEVEL_NUM})"
                required
              >
                <option value="">-- Select Grade --</option>
                <!-- Will be populated based on education level -->
              </select>
            </div>

            <div class="form-group">
              <label for="stream_id_{LEVEL_NUM}"
                >Select Stream (Optional):</label
              >
              <select
                name="stream_id_{LEVEL_NUM}"
                id="stream_id_{LEVEL_NUM}"
                class="stream-select form-control"
              >
                <option value="">-- All Streams --</option>
                <!-- Will be populated based on grade -->
              </select>
            </div>

            <div class="class-teacher-option">
              <label>
                <input
                  type="checkbox"
                  name="is_class_teacher_{LEVEL_NUM}"
                  id="is_class_teacher_{LEVEL_NUM}"
                  value="1"
                />
                Designate as Class Teacher
              </label>
            </div>
          </div>

          <div class="assignment-section">
            <div class="form-group">
              <label for="subjects_{LEVEL_NUM}"
                >Subjects (Hold Ctrl/Cmd to select multiple):</label
              >
              <select
                name="subjects_{LEVEL_NUM}[]"
                id="subjects-select-{LEVEL_NUM}"
                class="form-control"
                multiple
                style="min-height: 150px"
              >
                <!-- Will be populated based on education level -->
                <option disabled>Please select an education level first</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      // Global variables
      let levelCount = 1;
      // Map of education levels to grade IDs based on actual database values
      const educationLevelGrades = {
        lower_primary: [
          { id: 3, number: 1 }, // Grade 1 has ID 3
          { id: 4, number: 2 }, // Grade 2 has ID 4
          { id: 5, number: 3 }, // Grade 3 has ID 5
        ],
        upper_primary: [
          { id: 6, number: 4 }, // Grade 4 has ID 6
          { id: 7, number: 5 }, // Grade 5 has ID 7
          { id: 8, number: 6 }, // Grade 6 has ID 8
        ],
        junior_secondary: [
          { id: 9, number: 7 }, // Grade 7 has ID 9
          { id: 1, number: 8 }, // Grade 8 has ID 1
          { id: 2, number: 9 }, // Grade 9 has ID 2
        ],
      };

      // Function to toggle new teacher fields
      function toggleNewTeacherFields() {
        const newTeacherFields = document.getElementById("new-teacher-fields");
        const teacherSelect = document.getElementById("teacher_id");
        const createNewTeacher =
          document.getElementById("create_new_teacher").checked;

        if (createNewTeacher) {
          newTeacherFields.style.display = "block";
          teacherSelect.disabled = true;
          teacherSelect.required = false;

          // Make new teacher fields required
          document.getElementById("username").required = true;
          document.getElementById("password").required = true;
          document.getElementById("role").required = true;
        } else {
          newTeacherFields.style.display = "none";
          teacherSelect.disabled = false;
          teacherSelect.required = true;

          // Make new teacher fields not required
          document.getElementById("username").required = false;
          document.getElementById("password").required = false;
          document.getElementById("role").required = false;
        }
      }

      // Function to add another education level section
      function addEducationLevel() {
        levelCount++;

        // Get the template and replace placeholders
        const template = document.getElementById(
          "level-section-template"
        ).innerHTML;
        const newSection = template.replace(/{LEVEL_NUM}/g, levelCount);

        // Create a container for the new section
        const container = document.createElement("div");
        container.id = `level-section-${levelCount}`;
        container.innerHTML = newSection;

        // Add to the container
        document
          .getElementById("education-levels-container")
          .appendChild(container);

        // Update the form's hidden input to track the number of levels
        if (!document.getElementById("level_count")) {
          const levelCountInput = document.createElement("input");
          levelCountInput.type = "hidden";
          levelCountInput.id = "level_count";
          levelCountInput.name = "level_count";
          levelCountInput.value = levelCount;
          document
            .getElementById("bulk-assignment-form")
            .appendChild(levelCountInput);
        } else {
          document.getElementById("level_count").value = levelCount;
        }

        // Update the summary
        updateAssignmentSummary();
      }

      // Function to remove an education level section
      function removeEducationLevel(levelNum) {
        const section = document.getElementById(`level-section-${levelNum}`);
        if (section) {
          section.remove();
          updateAssignmentSummary();
        }
      }

      // Function to update grades based on selected education level
      function updateSubjects(levelNum) {
        const educationLevel = document.getElementById(
          `education_level_${levelNum}`
        ).value;
        const subjectsSelect = document.getElementById(
          `subjects-select-${levelNum}`
        );
        const gradeSelect = document.getElementById(`grade_id_${levelNum}`);

        // Clear existing options in grade select
        gradeSelect.innerHTML = '<option value="">-- Select Grade --</option>';

        // Clear existing options in subjects select
        subjectsSelect.innerHTML = "";

        if (!educationLevel) {
          subjectsSelect.innerHTML =
            "<option disabled>Please select an education level first</option>";
          return;
        }

        // Add grades based on education level
        const grades = educationLevelGrades[educationLevel] || [];
        grades.forEach((grade) => {
          const option = document.createElement("option");
          option.value = grade.id; // Use the grade ID as the value
          option.textContent = `Grade ${grade.number}`; // Display the grade number
          option.dataset.gradeNumber = grade.number; // Store the grade number for reference
          gradeSelect.appendChild(option);
        });

        // Fetch subjects from the server based on education level
        fetch(
          `/get_subjects_by_education_level?education_level=${educationLevel}`
        )
          .then((response) => response.json())
          .then((data) => {
            const subjects = data.subjects || [];

            if (subjects.length > 0) {
              subjects.forEach((subject) => {
                const option = document.createElement("option");
                option.value = subject.id;
                option.textContent = subject.name;
                option.dataset.subject = subject.name;
                option.dataset.level = levelNum;
                subjectsSelect.appendChild(option);
              });

              // Add event listener for changes
              subjectsSelect.addEventListener(
                "change",
                updateAssignmentSummary
              );
            } else {
              const option = document.createElement("option");
              option.disabled = true;
              option.textContent = "No subjects found for this education level";
              subjectsSelect.appendChild(option);
            }

            // Update the summary
            updateAssignmentSummary();
          })
          .catch((error) => {
            console.error("Error fetching subjects:", error);
            const option = document.createElement("option");
            option.disabled = true;
            option.textContent = "Error loading subjects";
            subjectsSelect.appendChild(option);
          });

        // Subjects are now loaded dynamically from the server

        // Update the summary
        updateAssignmentSummary();
      }

      // Function to update streams based on selected grade
      function updateStreams(levelNum) {
        const gradeId = document.getElementById(`grade_id_${levelNum}`).value;
        const streamSelect = document.getElementById(`stream_id_${levelNum}`);

        // Clear existing options
        streamSelect.innerHTML = '<option value="">-- All Streams --</option>';

        if (!gradeId) {
          return;
        }

        console.log(`Updating streams for grade: ${gradeId}`);

        // Fetch streams from the server based on grade ID
        fetch(`/get_streams_by_grade?grade_id=${gradeId}`)
          .then((response) => response.json())
          .then((data) => {
            const streams = data.streams || [];

            if (streams.length > 0) {
              streams.forEach((stream) => {
                const option = document.createElement("option");
                option.value = stream.id;
                option.textContent = stream.name;
                streamSelect.appendChild(option);
              });
              console.log(
                `Added ${streams.length} streams for grade ${gradeId}`
              );
            } else {
              console.log(`No streams found for grade ${gradeId}`);
            }

            // Update the summary
            updateAssignmentSummary();
          })
          .catch((error) => {
            console.error("Error fetching streams:", error);
            const option = document.createElement("option");
            option.disabled = true;
            option.textContent = "Error loading streams";
            streamSelect.appendChild(option);
          });

        // Update the summary
        updateAssignmentSummary();
      }

      // Function to update the assignment summary
      function updateAssignmentSummary() {
        const summaryDiv = document.getElementById("assignment-summary");
        const summaryContent = document.getElementById("summary-content");

        // Check if we have any assignments to summarize
        let hasAssignments = false;
        let summaryHtml = "";

        // Loop through each education level
        for (let i = 1; i <= levelCount; i++) {
          const levelSection = document.getElementById(`level-section-${i}`);
          if (!levelSection) continue;

          const educationLevelSelect = document.getElementById(
            `education_level_${i}`
          );
          const gradeSelect = document.getElementById(`grade_id_${i}`);
          const streamSelect = document.getElementById(`stream_id_${i}`);
          const isClassTeacher = document.getElementById(
            `is_class_teacher_${i}`
          );

          if (!educationLevelSelect || !educationLevelSelect.value) continue;

          const educationLevel =
            educationLevelSelect.options[educationLevelSelect.selectedIndex]
              .text;
          // Get the grade number from the selected option's dataset
          const gradeNumber =
            gradeSelect.selectedOptions[0]?.dataset?.gradeNumber;
          const grade = gradeSelect.value
            ? `Grade ${gradeNumber || gradeSelect.value}`
            : "";
          const stream = streamSelect.value
            ? streamSelect.options[streamSelect.selectedIndex].text
            : "All Streams";

          // Get selected subjects
          const subjectsSelect = document.getElementById(
            `subjects-select-${i}`
          );
          const selectedOptions = Array.from(subjectsSelect.selectedOptions);
          const subjects = selectedOptions.map((option) => option.textContent);

          if (grade && subjects.length > 0) {
            hasAssignments = true;

            summaryHtml += `
              <div class="summary-item">
                <strong>${educationLevel}</strong> - ${grade}, ${stream}
                <br>
                <strong>Subjects:</strong> ${subjects.join(", ")}
                ${
                  isClassTeacher.checked
                    ? "<br><strong>Class Teacher</strong>"
                    : ""
                }
              </div>
            `;
          }
        }

        if (hasAssignments) {
          summaryContent.innerHTML = summaryHtml;
          summaryDiv.style.display = "block";
        } else {
          summaryDiv.style.display = "none";
        }
      }

      // Initialize the form
      document.addEventListener("DOMContentLoaded", function () {
        // Add level count hidden input
        const levelCountInput = document.createElement("input");
        levelCountInput.type = "hidden";
        levelCountInput.id = "level_count";
        levelCountInput.name = "level_count";
        levelCountInput.value = levelCount;
        document
          .getElementById("bulk-assignment-form")
          .appendChild(levelCountInput);

        // Auto-hide messages after 5 seconds
        setTimeout(function () {
          const messages = document.querySelectorAll(".message");
          messages.forEach((message) => {
            message.style.display = "none";
          });
        }, 5000);
      });
    </script>
  </body>
</html>
