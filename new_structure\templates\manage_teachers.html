<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Manage Teachers - Hillview School (Class Teacher)</title>
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/style.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/manage_pages_enhanced.css') }}"
    />
    <style>
      /* Override container styles for manage pages */
      .manage-container {
        max-width: 95% !important;
        width: 95% !important;
        margin: 80px auto 60px !important;
        padding: var(--spacing-xl) !important;
      }

      /* Page header styling */
      .page-header {
        background: var(--white);
        padding: var(--spacing-lg);
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
        margin-bottom: var(--spacing-xl);
      }

      .page-header h1 {
        color: var(--primary-color);
        margin-bottom: var(--spacing-sm);
        font-size: 2.5rem;
      }

      .nav-links a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
        margin-right: var(--spacing-md);
      }

      .nav-links a:hover {
        text-decoration: underline;
      }

      /* Forms grid layout */
      .forms-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-xl);
        margin: var(--spacing-xl) 0;
      }

      /* Section styling */
      .form-card,
      .students-section {
        background: var(--white);
        padding: var(--spacing-lg);
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
        margin-bottom: var(--spacing-xl);
      }

      .form-card h2,
      .students-section h2 {
        color: var(--primary-color);
        margin-bottom: var(--spacing-lg);
        font-size: 1.3rem;
      }

      /* Form styling */
      .form-group {
        margin-bottom: var(--spacing-lg);
      }

      .form-group label {
        display: block;
        margin-bottom: var(--spacing-sm);
        color: var(--text-dark);
        font-weight: 500;
      }

      .form-control {
        width: 100%;
        padding: var(--spacing-md);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        font-size: 1rem;
        color: var(--text-dark);
        background: var(--white);
      }

      .form-control:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(31, 125, 83, 0.1);
      }

      /* Button styling */
      .manage-btn {
        background: var(--primary-color);
        color: var(--white);
        border: none;
        padding: var(--spacing-md) var(--spacing-lg);
        border-radius: var(--border-radius);
        cursor: pointer;
        transition: var(--transition);
        font-size: 1rem;
        font-weight: 500;
      }

      .manage-btn:hover {
        background: var(--secondary-color);
      }

      /* Action buttons */
      .action-buttons {
        display: flex;
        gap: var(--spacing-sm);
        flex-wrap: wrap;
      }

      .edit-btn,
      .delete-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        border: none;
        border-radius: var(--border-radius);
        cursor: pointer;
        font-size: 0.9rem;
        transition: var(--transition);
        text-decoration: none;
        display: inline-block;
        text-align: center;
      }

      .edit-btn {
        background: var(--warning-color);
        color: var(--text-dark);
      }

      .edit-btn:hover {
        background: #e0a800;
        text-decoration: none;
      }

      .delete-btn {
        background: var(--error-color);
        color: var(--white);
      }

      .delete-btn:hover {
        background: #c82333;
      }

      /* Table improvements */
      .table-responsive {
        overflow-x: auto;
        margin: var(--spacing-lg) 0;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-md);
        background: var(--white);
      }

      table {
        width: 100%;
        min-width: 800px;
        border-collapse: collapse;
      }

      th,
      td {
        padding: var(--spacing-md);
        text-align: left;
        border-bottom: 1px solid var(--border-color);
      }

      th {
        background: var(--primary-color);
        color: var(--white);
        font-weight: 600;
        position: sticky;
        top: 0;
        z-index: 10;
      }

      tr:nth-child(even) {
        background: rgba(31, 125, 83, 0.05);
      }

      tr:hover {
        background: rgba(31, 125, 83, 0.1);
      }

      /* Assignment sections */
      .assignment-section h3 {
        color: var(--primary-color);
        margin-bottom: var(--spacing-md);
        font-size: 1.2rem;
      }

      /* Search styling */
      #student-filter {
        margin-bottom: var(--spacing-lg);
      }

      #student-filter label {
        display: block;
        margin-bottom: var(--spacing-sm);
        color: var(--text-dark);
        font-weight: 500;
      }

      #teacher-search {
        width: 100%;
        max-width: 400px;
        padding: var(--spacing-md);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
      }

      /* Responsive design */
      @media (max-width: 768px) {
        .manage-container {
          max-width: 98% !important;
          width: 98% !important;
          padding: var(--spacing-md) !important;
        }

        .forms-grid {
          grid-template-columns: 1fr;
          gap: var(--spacing-lg);
        }

        .action-buttons {
          flex-direction: column;
        }
      }

      /* Enhanced Teacher Management Styles */
      .filter-controls {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border: 1px solid #e9ecef;
      }

      .filter-controls label {
        font-weight: 600;
        color: #495057;
        margin-right: 8px;
      }

      .filter-controls select {
        border: 1px solid #ced4da;
        border-radius: 4px;
        background: white;
        color: #495057;
      }

      /* Status and Role Badges */
      .status-badge {
        display: inline-block;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.75em;
        font-weight: 600;
        margin-left: 8px;
      }

      .status-badge.class-teacher {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      .role-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 0.85em;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .role-badge.teacher {
        background: #e3f2fd;
        color: #1565c0;
        border: 1px solid #bbdefb;
      }

      .role-badge.classteacher {
        background: #e8f5e8;
        color: #2e7d32;
        border: 1px solid #c8e6c9;
      }

      .role-badge.headteacher {
        background: #fff3e0;
        color: #ef6c00;
        border: 1px solid #ffcc02;
      }

      .status-active {
        color: #28a745;
        font-weight: 600;
      }

      .status-inactive {
        color: #6c757d;
        font-style: italic;
      }

      /* Subject and Assignment Display */
      .subjects-list {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
      }

      .subject-tag {
        background: #e7f3ff;
        color: #0066cc;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.8em;
        border: 1px solid #b3d9ff;
      }

      .more-subjects {
        color: #6c757d;
        font-size: 0.8em;
        font-style: italic;
      }

      .class-assignments {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }

      .class-assignment {
        background: #f0f8f0;
        padding: 4px 8px;
        border-radius: 6px;
        border-left: 3px solid #28a745;
      }

      .class-assignment .stream {
        background: #28a745;
        color: white;
        padding: 1px 6px;
        border-radius: 8px;
        font-size: 0.75em;
        margin-left: 6px;
      }

      .assignment-count {
        background: #6f42c1;
        color: white;
        padding: 4px 8px;
        border-radius: 50%;
        font-weight: 600;
        font-size: 0.9em;
        min-width: 24px;
        text-align: center;
        display: inline-block;
      }

      .no-data {
        color: #6c757d;
        font-style: italic;
        font-size: 0.9em;
      }

      /* Enhanced Action Buttons */
      .action-buttons {
        display: flex;
        gap: 6px;
        align-items: center;
      }

      .view-btn,
      .edit-btn {
        background: none;
        border: none;
        font-size: 1.2em;
        cursor: pointer;
        padding: 6px;
        border-radius: 4px;
        transition: background-color 0.2s;
      }

      .view-btn:hover {
        background: #e3f2fd;
      }

      .edit-btn:hover {
        background: #fff3e0;
      }

      .delete-btn {
        background: none;
        border: none;
        font-size: 1.1em;
        cursor: pointer;
        padding: 6px;
        border-radius: 4px;
        transition: background-color 0.2s;
      }

      .delete-btn:hover {
        background: #ffebee;
      }

      /* Table Enhancements */
      #teachers-table th {
        background: #f8f9fa;
        font-weight: 600;
        color: #495057;
        border-bottom: 2px solid #dee2e6;
      }

      #teachers-table tbody tr:hover {
        background: #f8f9fa;
        transition: background-color 0.2s;
      }

      #teachers-table td {
        vertical-align: middle;
        padding: 12px 8px;
      }

      /* Loading animation */
      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      /* Auto-hide error messages */
      .message-error.auto-hide {
        animation: fadeOut 3s ease-in-out 2s forwards;
      }

      @keyframes fadeOut {
        0% {
          opacity: 1;
        }
        100% {
          opacity: 0;
          display: none;
        }
      }
    </style>
  </head>
  <body>
    <div class="manage-container">
      <header class="page-header">
        <h1>👨‍🏫 Manage Teachers</h1>
        <p class="page-subtitle">
          Add, edit, and manage teacher profiles and information
        </p>
        <div class="nav-links">
          <a href="{{ url_for('classteacher.teacher_management_hub') }}"
            >Teacher Hub</a
          >
          <a href="{{ url_for('classteacher.dashboard') }}">Dashboard</a>
          <a href="{{ url_for('auth.logout_route') }}">Logout</a>
        </div>
      </header>

      <!-- Message container for notifications -->
      <div id="message-container">
        {% if error_message %}
        <div class="message message-error" id="error-message">
          {{ error_message }} {% if "initialization" in error_message.lower() %}
          <button
            onclick="location.reload()"
            style="
              margin-left: 10px;
              padding: 5px 10px;
              background: #dc3545;
              color: white;
              border: none;
              border-radius: 4px;
              cursor: pointer;
            "
          >
            Refresh Page
          </button>
          {% endif %}
        </div>
        {% endif %} {% if success_message %}
        <div class="message message-success">{{ success_message }}</div>
        {% endif %}
      </div>

      <!-- Loading indicator -->
      <div
        id="loading-indicator"
        style="
          display: none;
          text-align: center;
          padding: 20px;
          background: #f8f9fa;
          border-radius: 8px;
          margin-bottom: 20px;
        "
      >
        <div
          style="
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          "
        ></div>
        <span style="margin-left: 10px; color: #6c757d"
          >Loading teacher data...</span
        >
      </div>

      <div class="forms-grid">
        <!-- Add Teacher Form -->
        <div class="form-card">
          <h2>➕ Add New Teacher</h2>
          <form
            method="POST"
            action="{{ url_for('classteacher.manage_teachers') }}"
          >
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />

            <!-- Basic Information -->
            <div
              style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px"
            >
              <div class="form-group">
                <label for="first_name">First Name:</label>
                <input
                  type="text"
                  name="first_name"
                  id="first_name"
                  class="form-control"
                  placeholder="Enter first name"
                />
              </div>

              <div class="form-group">
                <label for="last_name">Last Name:</label>
                <input
                  type="text"
                  name="last_name"
                  id="last_name"
                  class="form-control"
                  placeholder="Enter last name"
                />
              </div>
            </div>

            <div class="form-group">
              <label for="username"
                >Username: <span style="color: red">*</span></label
              >
              <input
                type="text"
                name="username"
                id="username"
                class="form-control"
                required
                placeholder="Enter unique username"
              />
            </div>

            <div class="form-group">
              <label for="password"
                >Password: <span style="color: red">*</span></label
              >
              <input
                type="password"
                name="password"
                id="password"
                class="form-control"
                required
                placeholder="Enter secure password"
              />
            </div>

            <div class="form-group">
              <label for="role">Role: <span style="color: red">*</span></label>
              <select name="role" id="role" class="form-control" required>
                <option value="">Select Role</option>
                <option value="teacher">Teacher</option>
                <option value="classteacher">Class Teacher</option>
                <option value="headteacher">Head Teacher</option>
              </select>
            </div>

            <!-- Contact Information -->
            <div
              style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px"
            >
              <div class="form-group">
                <label for="email">Email:</label>
                <input
                  type="email"
                  name="email"
                  id="email"
                  class="form-control"
                  placeholder="<EMAIL>"
                />
              </div>

              <div class="form-group">
                <label for="phone">Phone:</label>
                <input
                  type="tel"
                  name="phone"
                  id="phone"
                  class="form-control"
                  placeholder="+254 700 000 000"
                />
              </div>
            </div>

            <!-- Professional Information -->
            <div
              style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px"
            >
              <div class="form-group">
                <label for="qualification">Qualification:</label>
                <select
                  name="qualification"
                  id="qualification"
                  class="form-control"
                >
                  <option value="">Select Qualification</option>
                  <option value="P1">P1 Certificate</option>
                  <option value="DIPLOMA">Diploma</option>
                  <option value="DEGREE">Bachelor's Degree</option>
                  <option value="MASTERS">Master's Degree</option>
                  <option value="PHD">PhD</option>
                </select>
              </div>

              <div class="form-group">
                <label for="specialization">Subject Specialization:</label>
                <select
                  name="specialization"
                  id="specialization"
                  class="form-control"
                >
                  <option value="">Select Specialization</option>
                  <!-- Lower Primary Specializations -->
                  <optgroup label="Lower Primary (PP1-Grade 3)">
                    <option value="Early Childhood Education">
                      Early Childhood Education
                    </option>
                    <option value="Mathematics Activities">
                      Mathematics Activities
                    </option>
                    <option value="English Activities">
                      English Activities
                    </option>
                    <option value="Kiswahili Activities">
                      Kiswahili Activities
                    </option>
                    <option value="Environmental Activities">
                      Environmental Activities
                    </option>
                    <option value="Religious Activities">
                      Religious Activities
                    </option>
                    <option value="Indigenous Language">
                      Indigenous Language
                    </option>
                  </optgroup>

                  <!-- Upper Primary Specializations -->
                  <optgroup label="Upper Primary (Grade 4-6)">
                    <option value="Mathematics">Mathematics</option>
                    <option value="English">English Language</option>
                    <option value="Kiswahili">Kiswahili</option>
                    <option value="Science and Technology">
                      Science and Technology
                    </option>
                    <option value="Social Studies">Social Studies</option>
                    <option value="Creative Arts">Creative Arts</option>
                    <option value="Agriculture and Nutrition">
                      Agriculture and Nutrition
                    </option>
                    <option value="Religious Education">
                      Religious Education
                    </option>
                  </optgroup>

                  <!-- Junior Secondary Specializations -->
                  <optgroup label="Junior Secondary (Grade 7-9)">
                    <option value="Mathematics (JS)">Mathematics</option>
                    <option value="English (JS)">English Language</option>
                    <option value="Kiswahili (JS)">Kiswahili</option>
                    <option value="Integrated Science">
                      Integrated Science
                    </option>
                    <option value="Social Studies (JS)">Social Studies</option>
                    <option value="Creative Arts and Sports">
                      Creative Arts and Sports
                    </option>
                    <option value="Pre-Technical Studies">
                      Pre-Technical Studies
                    </option>
                    <option value="Agriculture">Agriculture</option>
                    <option value="Religious Education (JS)">
                      Religious Education
                    </option>
                  </optgroup>

                  <!-- Administrative Specializations -->
                  <optgroup label="Administrative">
                    <option value="School Administration">
                      School Administration
                    </option>
                    <option value="Curriculum Development">
                      Curriculum Development
                    </option>
                    <option value="Educational Leadership">
                      Educational Leadership
                    </option>
                    <option value="Special Needs Education">
                      Special Needs Education
                    </option>
                    <option value="Guidance and Counseling">
                      Guidance and Counseling
                    </option>
                  </optgroup>
                </select>
              </div>
            </div>

            <button
              type="submit"
              name="add_teacher"
              class="manage-btn"
              style="width: 100%; margin-top: 15px"
            >
              ➕ Add Teacher
            </button>
          </form>
        </div>

        <!-- This is the second column for tips -->
        <div class="form-card">
          <h2>💡 Teacher Management Tips</h2>
          <div class="tips-content">
            <p>Here are some tips for managing teachers effectively:</p>
            <ul>
              <li>Create unique usernames for each teacher account</li>
              <li>Ensure passwords are secure but easy to remember</li>
              <li>
                Assign appropriate subjects based on teacher specialization
              </li>
              <li>
                Be careful when deleting teacher accounts - this will remove all
                associated data
              </li>
              <li>
                Teachers must be assigned to a grade and stream to access class
                information
              </li>
              <li>
                Different roles have different levels of access in the system
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Teacher Assignments Section -->
      <div class="students-section">
        <h2>📚 My Teaching Assignments</h2>

        <!-- Class Teacher Assignments -->
        <div class="assignment-section" style="margin-bottom: 20px">
          <h3>Class Teacher Assignments</h3>
          {% if class_teacher_assignments %}
          <div class="table-responsive">
            <table>
              <thead>
                <tr>
                  <th>Grade</th>
                  <th>Stream</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for assignment in class_teacher_assignments %}
                <tr>
                  <td>
                    {{ assignment.grade.name if assignment.grade is not mapping
                    else assignment.grade }}
                  </td>
                  <td>
                    {% if assignment.stream is not mapping %} {{
                    assignment.stream.name if assignment.stream else 'All
                    Streams' }} {% else %} {{ assignment.stream.name if
                    assignment.stream else 'All Streams' }} {% endif %}
                  </td>
                  <td>
                    <div class="action-buttons">
                      <form
                        method="POST"
                        action="{{ url_for('classteacher.manage_teachers') }}"
                        style="display: inline"
                      >
                        <input
                          type="hidden"
                          name="csrf_token"
                          value="{{ csrf_token() }}"
                        />
                        <input
                          type="hidden"
                          name="assignment_id"
                          value="{{ assignment.id }}"
                        />
                        <input
                          type="hidden"
                          name="assignment_type"
                          value="class_teacher"
                        />
                        <button
                          type="submit"
                          name="delete_assignment"
                          class="delete-btn"
                          onclick="return confirm('Are you sure you want to remove this class teacher assignment?')"
                        >
                          Delete
                        </button>
                      </form>
                      <a
                        href="{{ url_for('bulk_assignments.edit_assignment', assignment_id=assignment.id, assignment_type='class_teacher') }}"
                        class="edit-btn"
                      >
                        Edit
                      </a>
                    </div>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
          {% else %}
          <p>
            You are not currently assigned as a class teacher for any class.
          </p>
          {% endif %}
        </div>

        <!-- Subject Assignments -->
        <div class="assignment-section" style="margin-bottom: 30px">
          <h3>Subject Assignments</h3>
          {% if subject_assignments %}
          <div class="table-responsive">
            <table>
              <thead>
                <tr>
                  <th>Subject</th>
                  <th>Education Level</th>
                  <th>Grade</th>
                  <th>Stream</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for assignment in subject_assignments %}
                <tr>
                  <td>{{ assignment.subject.name }}</td>
                  <td>
                    {{ assignment.subject.education_level|replace('_', '
                    ')|title if assignment.subject.education_level else 'N/A' }}
                  </td>
                  <td>{{ assignment.grade.name }}</td>
                  <td>
                    {{ assignment.stream.name if assignment.stream else 'All
                    Streams' }}
                  </td>
                  <td>
                    <div class="action-buttons">
                      <form
                        method="POST"
                        action="{{ url_for('classteacher.manage_teachers') }}"
                        style="display: inline"
                      >
                        <input
                          type="hidden"
                          name="csrf_token"
                          value="{{ csrf_token() }}"
                        />
                        <input
                          type="hidden"
                          name="assignment_id"
                          value="{{ assignment.id if assignment is not mapping else assignment['id'] }}"
                        />
                        <input
                          type="hidden"
                          name="assignment_type"
                          value="subject"
                        />
                        <button
                          type="submit"
                          name="delete_assignment"
                          class="delete-btn"
                          onclick="return confirm('Are you sure you want to remove this subject assignment?')"
                        >
                          Delete
                        </button>
                      </form>
                      <a
                        href="{{ url_for('bulk_assignments.edit_assignment', assignment_id=assignment.id, assignment_type='subject') }}"
                        class="edit-btn"
                      >
                        Edit
                      </a>
                    </div>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
          {% else %}
          <p>You are not currently assigned to teach any subjects.</p>
          {% endif %}
        </div>
      </div>

      <!-- Teachers Table -->
      <div class="students-section">
        <h2>👥 Existing Teachers</h2>
        <div id="student-filter">
          <label for="teacher-search">Search teachers:</label>
          <input
            type="text"
            id="teacher-search"
            onkeyup="searchTeachers()"
            placeholder="Type to search..."
          />
        </div>

        <!-- Filter and Sort Controls -->
        <div
          class="filter-controls"
          style="
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
          "
        >
          <div>
            <label for="role-filter">Filter by Role:</label>
            <select
              id="role-filter"
              onchange="filterTeachers()"
              style="padding: 5px"
            >
              <option value="">All Roles</option>
              <option value="teacher">Teacher</option>
              <option value="classteacher">Class Teacher</option>
              <option value="headteacher">Head Teacher</option>
            </select>
          </div>

          <div>
            <label for="assignment-filter">Filter by Assignment:</label>
            <select
              id="assignment-filter"
              onchange="filterTeachers()"
              style="padding: 5px"
            >
              <option value="">All Teachers</option>
              <option value="has-class">Has Class Assignment</option>
              <option value="has-subject">Has Subject Assignment</option>
              <option value="no-assignment">No Assignments</option>
            </select>
          </div>

          <div>
            <label for="sort-by">Sort by:</label>
            <select id="sort-by" onchange="sortTeachers()" style="padding: 5px">
              <option value="username">Username</option>
              <option value="role">Role</option>
              <option value="assignments">Total Assignments</option>
            </select>
          </div>
        </div>

        <div class="table-responsive">
          <table id="teachers-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Username</th>
                <th>Role</th>
                <th>Status</th>
                <th>Subjects Taught</th>
                <th>Class Assignments</th>
                <th>Total Assignments</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {% for teacher in teachers %}
              <tr
                data-role="{{ teacher.role }}"
                data-has-class="{{ 'true' if teacher.is_class_teacher else 'false' }}"
                data-has-subject="{{ 'true' if teacher.subjects_taught else 'false' }}"
                data-assignments="{{ teacher.total_assignments }}"
              >
                <td>{{ teacher.id }}</td>
                <td>
                  <strong>{{ teacher.username }}</strong>
                  {% if teacher.is_class_teacher %}
                  <span class="status-badge class-teacher">Class Teacher</span>
                  {% endif %}
                </td>
                <td>
                  <span class="role-badge {{ teacher.role }}">
                    {{ teacher.role|title }}
                  </span>
                </td>
                <td>
                  {% if teacher.total_assignments > 0 %}
                  <span class="status-active">Active</span>
                  {% else %}
                  <span class="status-inactive">No Assignments</span>
                  {% endif %}
                </td>
                <td>
                  {% if teacher.subjects_taught %}
                  <div class="subjects-list">
                    {% for subject in teacher.subjects_taught[:3] %}
                    <span class="subject-tag">{{ subject }}</span>
                    {% endfor %} {% if teacher.subjects_taught|length > 3 %}
                    <span class="more-subjects"
                      >+{{ teacher.subjects_taught|length - 3 }} more</span
                    >
                    {% endif %}
                  </div>
                  {% else %}
                  <span class="no-data">No subjects assigned</span>
                  {% endif %}
                </td>
                <td>
                  {% if teacher.class_assignments %}
                  <div class="class-assignments">
                    {% for assignment in teacher.class_assignments %}
                    <div class="class-assignment">
                      <strong>{{ assignment.grade }}</strong>
                      {% if assignment.stream != 'All Streams' %}
                      <span class="stream">{{ assignment.stream }}</span>
                      {% endif %}
                    </div>
                    {% endfor %}
                  </div>
                  {% else %}
                  <span class="no-data">No class assignments</span>
                  {% endif %}
                </td>
                <td>
                  <span class="assignment-count"
                    >{{ teacher.total_assignments }}</span
                  >
                </td>
                <td>
                  <div class="action-buttons">
                    <button
                      class="view-btn"
                      onclick="viewTeacherDetails({{ teacher.id }}, '{{ teacher.username }}')"
                      title="View Details"
                    >
                      👁️
                    </button>
                    <button
                      class="edit-btn"
                      onclick="editTeacher({{ teacher.id }}, '{{ teacher.username }}', '{{ teacher.role }}')"
                      title="Edit Teacher"
                    >
                      ✏️
                    </button>
                    <form
                      method="POST"
                      action="{{ url_for('classteacher.manage_teachers') }}"
                      style="display: inline"
                      onsubmit="return confirm('Are you sure you want to delete {{ teacher.username }}? This will remove all their assignments.')"
                    >
                      <input
                        type="hidden"
                        name="csrf_token"
                        value="{{ csrf_token() }}"
                      />
                      <input
                        type="hidden"
                        name="teacher_id"
                        value="{{ teacher.id }}"
                      />
                      <button
                        type="submit"
                        name="delete_teacher"
                        class="delete-btn"
                        title="Delete Teacher"
                      >
                        🗑️
                      </button>
                    </form>
                  </div>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <script>
      // Function to get the correct teacher streams API endpoint based on current context
      function getTeacherStreamsEndpoint(gradeId) {
        // Check if we're in headteacher universal context
        if (window.location.pathname.includes("/universal/")) {
          return `/universal/api/streams/${gradeId}`;
        }
        // Check if we're in headteacher context (admin routes)
        else if (
          window.location.pathname.includes("/headteacher/") ||
          window.location.pathname.includes("/admin/")
        ) {
          return `/admin/api/teacher_streams/${gradeId}`;
        }
        // Default to classteacher endpoint
        return `/classteacher/teacher_streams/${gradeId}`;
      }

      // Function to extract streams from response data (handles different response formats)
      function extractStreamsFromResponse(data) {
        // Handle different response formats
        if (data.streams) {
          return data.streams; // Admin blueprint format: {streams: [...]}
        } else if (data.success && data.streams) {
          return data.streams; // Universal blueprint format: {success: true, streams: [...]}
        }
        return []; // Fallback to empty array
      }

      function updateStreams() {
        console.log("updateStreams called");
        const gradeId = document.getElementById("grade").value;
        console.log("Grade ID:", gradeId);
        const streamSelect = document.getElementById("stream");

        // Clear existing options
        streamSelect.innerHTML = '<option value="">Select Stream</option>';

        if (gradeId) {
          // Fetch streams for the selected grade using dynamic endpoint
          const endpoint = getTeacherStreamsEndpoint(gradeId);
          console.log("Using teacher streams endpoint:", endpoint);

          fetch(endpoint, {
            credentials: "same-origin",
          })
            .then((response) => response.json())
            .then((data) => {
              const streams = extractStreamsFromResponse(data);
              streams.forEach((stream) => {
                const option = document.createElement("option");
                option.value = stream.id;
                option.textContent = stream.name;
                streamSelect.appendChild(option);
              });
            })
            .catch((error) => {
              console.error("Error fetching streams:", error);
              // Fallback: try the other endpoint if first one fails
              const fallbackEndpoint = gradeId
                ? endpoint.includes("/headteacher/")
                  ? `/classteacher/teacher_streams/${gradeId}`
                  : `/headteacher/api/teacher_streams/${gradeId}`
                : null;

              if (fallbackEndpoint) {
                console.log(
                  "Trying teacher streams fallback endpoint:",
                  fallbackEndpoint
                );
                fetch(fallbackEndpoint, {
                  credentials: "same-origin",
                })
                  .then((response) => response.json())
                  .then((data) => {
                    const streams = extractStreamsFromResponse(data);
                    streams.forEach((stream) => {
                      const option = document.createElement("option");
                      option.value = stream.id;
                      option.textContent = stream.name;
                      streamSelect.appendChild(option);
                    });
                  })
                  .catch((fallbackError) => {
                    console.error(
                      "Teacher streams fallback endpoint also failed:",
                      fallbackError
                    );
                  });
              }
            });
        }
      }

      // Enhanced search function
      function searchTeachers() {
        const searchValue = document
          .getElementById("teacher-search")
          .value.toLowerCase();
        const rows = document.querySelectorAll("#teachers-table tbody tr");

        rows.forEach((row) => {
          const teacherName = row.cells[1].textContent.toLowerCase();
          const subjects = row.cells[4].textContent.toLowerCase();
          const role = row.cells[2].textContent.toLowerCase();

          if (
            teacherName.includes(searchValue) ||
            subjects.includes(searchValue) ||
            role.includes(searchValue)
          ) {
            row.style.display = "";
          } else {
            row.style.display = "none";
          }
        });
      }

      // Filter teachers by role and assignment status
      function filterTeachers() {
        const roleFilter = document.getElementById("role-filter").value;
        const assignmentFilter =
          document.getElementById("assignment-filter").value;
        const rows = document.querySelectorAll("#teachers-table tbody tr");

        rows.forEach((row) => {
          let showRow = true;

          // Role filter
          if (roleFilter && row.dataset.role !== roleFilter) {
            showRow = false;
          }

          // Assignment filter
          if (assignmentFilter) {
            switch (assignmentFilter) {
              case "has-class":
                if (row.dataset.hasClass !== "true") showRow = false;
                break;
              case "has-subject":
                if (row.dataset.hasSubject !== "true") showRow = false;
                break;
              case "no-assignment":
                if (parseInt(row.dataset.assignments) > 0) showRow = false;
                break;
            }
          }

          row.style.display = showRow ? "" : "none";
        });
      }

      // Sort teachers
      function sortTeachers() {
        const sortBy = document.getElementById("sort-by").value;
        const tbody = document.querySelector("#teachers-table tbody");
        const rows = Array.from(tbody.querySelectorAll("tr"));

        rows.sort((a, b) => {
          let aValue, bValue;

          switch (sortBy) {
            case "username":
              aValue = a.cells[1].textContent.trim().toLowerCase();
              bValue = b.cells[1].textContent.trim().toLowerCase();
              return aValue.localeCompare(bValue);
            case "role":
              aValue = a.dataset.role;
              bValue = b.dataset.role;
              return aValue.localeCompare(bValue);
            case "assignments":
              aValue = parseInt(a.dataset.assignments);
              bValue = parseInt(b.dataset.assignments);
              return bValue - aValue; // Descending order
            default:
              return 0;
          }
        });

        // Re-append sorted rows
        rows.forEach((row) => tbody.appendChild(row));
      }

      // View teacher details
      function viewTeacherDetails(teacherId, username) {
        // Create modal or redirect to detailed view
        alert(
          `Viewing details for ${username} (ID: ${teacherId})\n\nThis feature will show:\n- Complete assignment history\n- Performance metrics\n- Student feedback\n- Schedule overview`
        );
      }

      // Enhanced edit teacher with modal
      function editTeacher(teacherId, username, role) {
        // Create modal HTML
        const modalHTML = `
          <div id="editTeacherModal" style="
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
            align-items: center; justify-content: center;
          ">
            <div style="
              background: white; padding: 30px; border-radius: 10px;
              width: 90%; max-width: 600px; max-height: 90vh; overflow-y: auto;
              box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            ">
              <h3 style="margin-bottom: 20px; color: #1f7d53;">✏️ Edit Teacher: ${username}</h3>

              <form method="POST" action="{{ url_for('classteacher.manage_teachers') }}">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <input type="hidden" name="edit_teacher_id" value="${teacherId}">

                <!-- Basic Information -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                  <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: 600;">First Name:</label>
                    <input type="text" name="edit_first_name" class="form-control" placeholder="Enter first name">
                  </div>
                  <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: 600;">Last Name:</label>
                    <input type="text" name="edit_last_name" class="form-control" placeholder="Enter last name">
                  </div>
                </div>

                <div style="margin-bottom: 15px;">
                  <label style="display: block; margin-bottom: 5px; font-weight: 600;">Role:</label>
                  <select name="edit_role" class="form-control" required>
                    <option value="teacher" ${
                      role === "teacher" ? "selected" : ""
                    }>Teacher</option>
                    <option value="classteacher" ${
                      role === "classteacher" ? "selected" : ""
                    }>Class Teacher</option>
                    <option value="headteacher" ${
                      role === "headteacher" ? "selected" : ""
                    }>Head Teacher</option>
                  </select>
                </div>

                <!-- Contact Information -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                  <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: 600;">Email:</label>
                    <input type="email" name="edit_email" class="form-control" placeholder="<EMAIL>">
                  </div>
                  <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: 600;">Phone:</label>
                    <input type="tel" name="edit_phone" class="form-control" placeholder="+254 700 000 000">
                  </div>
                </div>

                <!-- Professional Information -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                  <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: 600;">Qualification:</label>
                    <select name="edit_qualification" class="form-control">
                      <option value="">Select Qualification</option>
                      <option value="P1">P1 Certificate</option>
                      <option value="DIPLOMA">Diploma</option>
                      <option value="DEGREE">Bachelor's Degree</option>
                      <option value="MASTERS">Master's Degree</option>
                      <option value="PHD">PhD</option>
                    </select>
                  </div>
                  <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: 600;">Specialization:</label>
                    <select name="edit_specialization" class="form-control">
                      <option value="">Select Specialization</option>
                      <optgroup label="Lower Primary">
                        <option value="Early Childhood Education">Early Childhood Education</option>
                        <option value="Mathematics Activities">Mathematics Activities</option>
                        <option value="English Activities">English Activities</option>
                        <option value="Environmental Activities">Environmental Activities</option>
                      </optgroup>
                      <optgroup label="Upper Primary">
                        <option value="Mathematics">Mathematics</option>
                        <option value="English">English Language</option>
                        <option value="Science and Technology">Science and Technology</option>
                        <option value="Social Studies">Social Studies</option>
                      </optgroup>
                      <optgroup label="Junior Secondary">
                        <option value="Mathematics (JS)">Mathematics</option>
                        <option value="Integrated Science">Integrated Science</option>
                        <option value="Social Studies (JS)">Social Studies</option>
                        <option value="Creative Arts and Sports">Creative Arts and Sports</option>
                      </optgroup>
                    </select>
                  </div>
                </div>

                <div style="margin-bottom: 20px;">
                  <label style="display: block; margin-bottom: 5px; font-weight: 600;">New Password (optional):</label>
                  <input type="password" name="edit_password" class="form-control" placeholder="Leave blank to keep current password">
                </div>

                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                  <button type="button" onclick="closeEditModal()" style="
                    background: #6c757d; color: white; border: none; padding: 10px 20px;
                    border-radius: 5px; cursor: pointer;
                  ">Cancel</button>
                  <button type="submit" name="update_teacher" style="
                    background: #1f7d53; color: white; border: none; padding: 10px 20px;
                    border-radius: 5px; cursor: pointer;
                  ">💾 Save Changes</button>
                </div>
              </form>
            </div>
          </div>
        `;

        // Add modal to page
        document.body.insertAdjacentHTML("beforeend", modalHTML);

        // Add click outside to close
        document
          .getElementById("editTeacherModal")
          .addEventListener("click", function (e) {
            if (e.target === this) {
              closeEditModal();
            }
          });
      }

      function closeEditModal() {
        const modal = document.getElementById("editTeacherModal");
        if (modal) {
          modal.remove();
        }
      }

      // Function to auto-hide messages after 5 seconds
      document.addEventListener("DOMContentLoaded", function () {
        // Handle initialization error messages differently
        const errorMessage = document.getElementById("error-message");
        if (
          errorMessage &&
          errorMessage.textContent.includes("initialization")
        ) {
          // Auto-hide initialization messages after 3 seconds
          setTimeout(function () {
            errorMessage.classList.add("auto-hide");
          }, 3000);
        }

        // Auto-hide other messages after 5 seconds
        setTimeout(function () {
          const messages = document.querySelectorAll(
            ".message:not(.auto-hide)"
          );
          messages.forEach((message) => {
            if (!message.textContent.includes("initialization")) {
              message.style.display = "none";
            }
          });
        }, 5000);

        // Show loading indicator briefly on page load if there are no teachers
        const teachersTable = document.getElementById("teachers-table");
        const loadingIndicator = document.getElementById("loading-indicator");

        if (teachersTable && teachersTable.querySelector("tbody tr") === null) {
          loadingIndicator.style.display = "block";
          setTimeout(function () {
            loadingIndicator.style.display = "none";
          }, 2000);
        }
      });
    </script>
  </body>
</html>
