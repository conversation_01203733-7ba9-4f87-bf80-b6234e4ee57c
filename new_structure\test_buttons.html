<!DOCTYPE html>
<html>
<head>
    <title>Button Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        button { margin: 10px; padding: 10px 20px; }
        .btn-primary { background: #007bff; color: white; border: none; border-radius: 5px; }
        .btn-success { background: #28a745; color: white; border: none; border-radius: 5px; }
        .btn-warning { background: #ffc107; color: black; border: none; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🧪 Button Test Page</h1>
    <p>This page tests if JavaScript functions work properly.</p>
    
    <button class="btn-primary" onclick="testBasicAlert()">Test Basic Alert</button>
    <button class="btn-success" onclick="testFetch()">Test Fetch API</button>
    <button class="btn-warning" onclick="testSaveFunction()">Test Save Function</button>
    
    <div id="results" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc;"></div>
    
    <script>
        function testBasicAlert() {
            alert("✅ Basic JavaScript is working!");
            document.getElementById('results').innerHTML += "<p>✅ Basic alert test passed</p>";
        }
        
        function testFetch() {
            console.log("🧪 Testing fetch API...");
            document.getElementById('results').innerHTML += "<p>🔄 Testing fetch API...</p>";
            
            fetch("/api/subject-config/all")
                .then(response => {
                    console.log("Response status:", response.status);
                    return response.json();
                })
                .then(data => {
                    console.log("Response data:", data);
                    document.getElementById('results').innerHTML += 
                        `<p>✅ Fetch test passed! Found ${data.configurations?.length || 0} configurations</p>`;
                })
                .catch(error => {
                    console.error("Fetch error:", error);
                    document.getElementById('results').innerHTML += 
                        `<p>❌ Fetch test failed: ${error.message}</p>`;
                });
        }
        
        function testSaveFunction() {
            console.log("🧪 Testing save function...");
            document.getElementById('results').innerHTML += "<p>🔄 Testing save function...</p>";
            
            fetch("/api/subject-config/save-all", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                }
            })
            .then(response => {
                console.log("Save response status:", response.status);
                return response.json();
            })
            .then(data => {
                console.log("Save response data:", data);
                document.getElementById('results').innerHTML += 
                    `<p>✅ Save test passed! Message: ${data.message}</p>`;
            })
            .catch(error => {
                console.error("Save error:", error);
                document.getElementById('results').innerHTML += 
                    `<p>❌ Save test failed: ${error.message}</p>`;
            });
        }
    </script>
</body>
</html>
