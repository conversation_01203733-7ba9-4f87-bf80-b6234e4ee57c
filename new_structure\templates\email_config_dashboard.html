<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Configuration - Hillview School</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .email-dashboard {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .status-card.success {
            border-left: 5px solid #28a745;
        }
        
        .status-card.warning {
            border-left: 5px solid #ffc107;
        }
        
        .status-card.danger {
            border-left: 5px solid #dc3545;
        }
        
        .status-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }
        
        .status-icon.success {
            color: #28a745;
        }
        
        .status-icon.warning {
            color: #ffc107;
        }
        
        .status-icon.danger {
            color: #dc3545;
        }
        
        .status-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .status-desc {
            color: #666;
            font-size: 0.9em;
        }
        
        .config-section {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .config-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }
        
        .config-item:last-child {
            border-bottom: none;
        }
        
        .config-label {
            font-weight: bold;
            color: #666;
        }
        
        .config-value {
            color: #333;
            font-family: monospace;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: #28a745;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn-success:hover {
            background: #218838;
            transform: translateY(-1px);
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn-warning:hover {
            background: #e0a800;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .template-card {
            border: 1px solid #e1e5e9;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
        }
        
        .template-card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .template-type {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            margin-bottom: 10px;
            display: inline-block;
        }
        
        .template-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .template-status {
            display: flex;
            align-items: center;
            gap: 5px;
            margin-bottom: 15px;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .status-default {
            background-color: #cce5ff;
            color: #004085;
        }
        
        .template-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 0.9em;
        }
        
        .stats-row {
            display: flex;
            justify-content: space-around;
            text-align: center;
            margin-top: 20px;
        }
        
        .stat-item {
            flex: 1;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="email-dashboard">
        <!-- Header -->
        <div class="dashboard-header">
            <h1><i class="fas fa-envelope-open-text"></i> Email Configuration Dashboard</h1>
            <p>Manage parent notification system and email templates</p>
        </div>

        <!-- Status Overview -->
        <div class="status-grid">
            <div class="status-card {{ 'success' if smtp_configured else 'warning' }}">
                <div class="status-icon {{ 'success' if smtp_configured else 'warning' }}">
                    <i class="fas fa-{{ 'check-circle' if smtp_configured else 'exclamation-triangle' }}"></i>
                </div>
                <div class="status-title">SMTP Configuration</div>
                <div class="status-desc">
                    {{ 'Configured and ready' if smtp_configured else 'Not configured' }}
                </div>
            </div>
            
            <div class="status-card success">
                <div class="status-icon success">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="status-title">Email Templates</div>
                <div class="status-desc">{{ templates|length }} templates available</div>
            </div>
            
            <div class="status-card {{ 'success' if sent_emails > 0 else 'warning' }}">
                <div class="status-icon {{ 'success' if sent_emails > 0 else 'warning' }}">
                    <i class="fas fa-paper-plane"></i>
                </div>
                <div class="status-title">Email Delivery</div>
                <div class="status-desc">
                    {{ sent_emails }}/{{ total_emails }} emails sent successfully
                </div>
            </div>
        </div>

        <!-- SMTP Configuration -->
        <div class="config-section">
            <h2 class="section-title">
                <i class="fas fa-server"></i> SMTP Configuration
            </h2>
            
            {% if smtp_configured %}
                <div class="config-item">
                    <span class="config-label">SMTP Server:</span>
                    <span class="config-value">{{ smtp_server }}</span>
                </div>
                <div class="config-item">
                    <span class="config-label">SMTP Port:</span>
                    <span class="config-value">{{ smtp_port }}</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Username:</span>
                    <span class="config-value">{{ smtp_username }}</span>
                </div>
                
                <div style="margin-top: 20px;">
                    <a href="{{ url_for('email_config.test_email') }}" class="btn-success">
                        <i class="fas fa-paper-plane"></i> Send Test Email
                    </a>
                    <a href="{{ url_for('email_config.smtp_settings') }}" class="btn-primary">
                        <i class="fas fa-cog"></i> Update Settings
                    </a>
                </div>
            {% else %}
                <p style="color: #666; margin-bottom: 20px;">
                    SMTP is not configured. Parent email notifications will not work until you configure the email settings.
                </p>
                <a href="{{ url_for('email_config.smtp_settings') }}" class="btn-warning">
                    <i class="fas fa-cog"></i> Configure SMTP
                </a>
                <a href="{{ url_for('email_config.setup_guide') }}" class="btn-primary">
                    <i class="fas fa-book"></i> Setup Guide
                </a>
            {% endif %}
        </div>

        <!-- Email Templates -->
        <div class="config-section">
            <h2 class="section-title">
                <i class="fas fa-file-alt"></i> Email Templates
            </h2>
            
            {% if templates %}
                <div class="templates-grid">
                    {% for template in templates %}
                    <div class="template-card">
                        <div class="template-type">{{ template.template_type.replace('_', ' ').title() }}</div>
                        <div class="template-name">{{ template.name }}</div>
                        <div class="template-status">
                            <span class="status-badge {{ 'status-active' if template.is_active else 'status-inactive' }}">
                                {{ 'Active' if template.is_active else 'Inactive' }}
                            </span>
                            {% if template.is_default %}
                                <span class="status-badge status-default">Default</span>
                            {% endif %}
                        </div>
                        <div class="template-actions">
                            <a href="{{ url_for('email_config.view_template', template_id=template.id) }}" class="btn-primary btn-sm">
                                <i class="fas fa-eye"></i> View
                            </a>
                            <a href="{{ url_for('email_config.preview_template', template_id=template.id) }}" class="btn-success btn-sm">
                                <i class="fas fa-search"></i> Preview
                            </a>
                            <a href="{{ url_for('email_config.edit_template', template_id=template.id) }}" class="btn-warning btn-sm">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <p style="color: #666; text-align: center; padding: 30px;">
                    No email templates found. This is unusual - templates should be created automatically.
                </p>
            {% endif %}
            
            <div style="margin-top: 20px;">
                <a href="{{ url_for('email_config.templates') }}" class="btn-primary">
                    <i class="fas fa-list"></i> Manage All Templates
                </a>
            </div>
        </div>

        <!-- Email Statistics -->
        {% if total_emails > 0 %}
        <div class="config-section">
            <h2 class="section-title">
                <i class="fas fa-chart-bar"></i> Email Statistics
            </h2>
            
            <div class="stats-row">
                <div class="stat-item">
                    <div class="stat-number">{{ total_emails }}</div>
                    <div class="stat-label">Total Emails</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ sent_emails }}</div>
                    <div class="stat-label">Successfully Sent</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ failed_emails }}</div>
                    <div class="stat-label">Failed</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ ((sent_emails / total_emails * 100) | round(1)) if total_emails > 0 else 0 }}%</div>
                    <div class="stat-label">Success Rate</div>
                </div>
            </div>
            
            <div style="margin-top: 20px; text-align: center;">
                <a href="{{ url_for('email_config.email_logs') }}" class="btn-primary">
                    <i class="fas fa-list"></i> View Email Logs
                </a>
            </div>
        </div>
        {% endif %}

        <!-- Back Button -->
        <div style="text-align: center; margin-top: 30px;">
            <a href="{{ url_for('admin.dashboard') }}" class="btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="flash-messages" style="position: fixed; top: 20px; right: 20px; z-index: 1000;">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }}" 
                         style="margin-bottom: 10px; padding: 15px; border-radius: 8px; background: white; box-shadow: 0 4px 15px rgba(0,0,0,0.1); max-width: 400px;">
                        {{ message }}
                        <button type="button" class="close" onclick="this.parentElement.remove();" 
                                style="float: right; background: none; border: none; font-size: 1.2em; cursor: pointer;">&times;</button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <script>
        // Auto-hide flash messages
        setTimeout(function() {
            const flashMessages = document.querySelector('.flash-messages');
            if (flashMessages) {
                flashMessages.style.opacity = '0';
                flashMessages.style.transition = 'opacity 0.5s ease';
                setTimeout(() => flashMessages.remove(), 500);
            }
        }, 5000);
    </script>
</body>
</html>
