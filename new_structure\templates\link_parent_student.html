<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Link Parent & Student - Hillview School</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .link-container {
            max-width: 800px;
            margin: 40px auto;
            padding: 20px;
        }
        
        .form-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px 15px 0 0;
            text-align: center;
        }
        
        .form-body {
            background: white;
            padding: 30px;
            border-radius: 0 0 15px 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .search-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .search-box {
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            padding: 20px;
        }
        
        .search-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .search-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            margin-bottom: 15px;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .search-results {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
        }
        
        .result-item {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        
        .result-item:hover {
            background-color: #f8f9fa;
        }
        
        .result-item.selected {
            background-color: #e3f2fd;
            border-left: 4px solid #667eea;
        }
        
        .result-name {
            font-weight: bold;
            color: #333;
        }
        
        .result-details {
            color: #666;
            font-size: 0.9em;
        }
        
        .selected-info {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .selected-info h4 {
            color: #2e7d32;
            margin: 0 0 10px 0;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-primary:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .no-results {
            text-align: center;
            color: #666;
            padding: 20px;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="link-container">
        <div class="form-header">
            <h1><i class="fas fa-link"></i> Link Parent & Student</h1>
            <p>Connect parents with their children in the system</p>
        </div>
        
        <div class="form-body">
            <form method="POST" id="linkForm">
                <!-- Search Section -->
                <div class="search-section">
                    <!-- Parent Search -->
                    <div class="search-box">
                        <div class="search-title">
                            <i class="fas fa-user"></i> Select Parent
                        </div>
                        <input type="text" 
                               id="parentSearch" 
                               class="search-input" 
                               placeholder="Search by name or email...">
                        <div class="search-results" id="parentResults">
                            <div class="no-results">Start typing to search for parents</div>
                        </div>
                        <div id="selectedParent" class="selected-info" style="display: none;">
                            <h4><i class="fas fa-check-circle"></i> Selected Parent</h4>
                            <div id="selectedParentInfo"></div>
                        </div>
                        <input type="hidden" id="parent_id" name="parent_id">
                    </div>
                    
                    <!-- Student Search -->
                    <div class="search-box">
                        <div class="search-title">
                            <i class="fas fa-user-graduate"></i> Select Student
                        </div>
                        <input type="text" 
                               id="studentSearch" 
                               class="search-input" 
                               placeholder="Search by name or admission number...">
                        <div class="search-results" id="studentResults">
                            <div class="no-results">Start typing to search for students</div>
                        </div>
                        <div id="selectedStudent" class="selected-info" style="display: none;">
                            <h4><i class="fas fa-check-circle"></i> Selected Student</h4>
                            <div id="selectedStudentInfo"></div>
                        </div>
                        <input type="hidden" id="student_id" name="student_id">
                    </div>
                </div>
                
                <!-- Relationship Details -->
                <div class="form-group">
                    <label for="relationship_type" class="form-label">Relationship Type</label>
                    <select id="relationship_type" name="relationship_type" class="form-control">
                        <option value="parent">Parent</option>
                        <option value="guardian">Guardian</option>
                        <option value="relative">Relative</option>
                    </select>
                </div>
                
                <div class="checkbox-group">
                    <input type="checkbox" id="is_primary_contact" name="is_primary_contact">
                    <label for="is_primary_contact">Set as primary contact for this student</label>
                </div>
                
                <!-- Submit Button -->
                <div style="margin-top: 30px;">
                    <button type="submit" class="btn-primary" id="submitBtn" disabled>
                        <i class="fas fa-link"></i> Create Link
                    </button>
                </div>
            </form>
            
            <div style="text-align: center; margin-top: 20px;">
                <a href="{{ url_for('parent_management.dashboard') }}" class="btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="flash-messages" style="position: fixed; top: 20px; right: 20px; z-index: 1000;">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }}" 
                         style="margin-bottom: 10px; padding: 15px; border-radius: 8px; background: white; box-shadow: 0 4px 15px rgba(0,0,0,0.1); max-width: 400px;">
                        {{ message }}
                        <button type="button" class="close" onclick="this.parentElement.remove();" 
                                style="float: right; background: none; border: none; font-size: 1.2em; cursor: pointer;">&times;</button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <script>
        let selectedParentId = null;
        let selectedStudentId = null;
        
        // Parent search functionality
        document.getElementById('parentSearch').addEventListener('input', function() {
            const query = this.value.trim();
            if (query.length < 2) {
                document.getElementById('parentResults').innerHTML = '<div class="no-results">Start typing to search for parents</div>';
                return;
            }
            
            fetch(`{{ url_for('parent_management.search_parents') }}?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    const resultsDiv = document.getElementById('parentResults');
                    if (data.parents && data.parents.length > 0) {
                        resultsDiv.innerHTML = data.parents.map(parent => `
                            <div class="result-item" onclick="selectParent(${parent.id}, '${parent.name}', '${parent.email}')">
                                <div class="result-name">${parent.name}</div>
                                <div class="result-details">${parent.email} ${parent.phone ? '• ' + parent.phone : ''}</div>
                            </div>
                        `).join('');
                    } else {
                        resultsDiv.innerHTML = '<div class="no-results">No parents found</div>';
                    }
                })
                .catch(error => {
                    console.error('Error searching parents:', error);
                    document.getElementById('parentResults').innerHTML = '<div class="no-results">Error searching parents</div>';
                });
        });
        
        // Student search functionality
        document.getElementById('studentSearch').addEventListener('input', function() {
            const query = this.value.trim();
            if (query.length < 2) {
                document.getElementById('studentResults').innerHTML = '<div class="no-results">Start typing to search for students</div>';
                return;
            }
            
            fetch(`{{ url_for('parent_management.search_students') }}?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    const resultsDiv = document.getElementById('studentResults');
                    if (data.students && data.students.length > 0) {
                        resultsDiv.innerHTML = data.students.map(student => `
                            <div class="result-item" onclick="selectStudent(${student.id}, '${student.name}', '${student.admission_number}', '${student.class}')">
                                <div class="result-name">${student.name}</div>
                                <div class="result-details">${student.admission_number} • ${student.class}</div>
                            </div>
                        `).join('');
                    } else {
                        resultsDiv.innerHTML = '<div class="no-results">No students found</div>';
                    }
                })
                .catch(error => {
                    console.error('Error searching students:', error);
                    document.getElementById('studentResults').innerHTML = '<div class="no-results">Error searching students</div>';
                });
        });
        
        function selectParent(id, name, email) {
            selectedParentId = id;
            document.getElementById('parent_id').value = id;
            document.getElementById('selectedParentInfo').innerHTML = `<strong>${name}</strong><br>${email}`;
            document.getElementById('selectedParent').style.display = 'block';
            document.getElementById('parentSearch').value = name;
            document.getElementById('parentResults').innerHTML = '<div class="no-results">Parent selected</div>';
            checkFormComplete();
        }
        
        function selectStudent(id, name, admissionNumber, className) {
            selectedStudentId = id;
            document.getElementById('student_id').value = id;
            document.getElementById('selectedStudentInfo').innerHTML = `<strong>${name}</strong><br>${admissionNumber} • ${className}`;
            document.getElementById('selectedStudent').style.display = 'block';
            document.getElementById('studentSearch').value = name;
            document.getElementById('studentResults').innerHTML = '<div class="no-results">Student selected</div>';
            checkFormComplete();
        }
        
        function checkFormComplete() {
            const submitBtn = document.getElementById('submitBtn');
            if (selectedParentId && selectedStudentId) {
                submitBtn.disabled = false;
            } else {
                submitBtn.disabled = true;
            }
        }
        
        // Auto-hide flash messages
        setTimeout(function() {
            const flashMessages = document.querySelector('.flash-messages');
            if (flashMessages) {
                flashMessages.style.opacity = '0';
                flashMessages.style.transition = 'opacity 0.5s ease';
                setTimeout(() => flashMessages.remove(), 500);
            }
        }, 5000);
    </script>
</body>
</html>
