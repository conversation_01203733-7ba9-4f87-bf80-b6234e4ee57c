<!-- Academic Performance Analytics Dashboard Components -->

<!-- Top Performers Component -->
<div class="analytics-component top-performers-component">
  <div class="component-header">
    <h3><i class="fas fa-trophy"></i> Top Performers</h3>
    <div class="component-actions">
      <button
        class="modern-btn btn-sm btn-outline"
        onclick="refreshAnalytics('top_performers')"
      >
        <i class="fas fa-sync-alt"></i> Refresh
      </button>
    </div>
  </div>

  <div class="component-content">
    <div id="top-performers-container">
      <!-- Top performers will be populated by JavaScript -->
      <div class="loading-state">
        <i class="fas fa-spinner fa-spin"></i> Loading top performers...
      </div>
    </div>
  </div>
</div>

<!-- Subject Performance Component -->
<div class="analytics-component subject-performance-component">
  <div class="component-header">
    <h3><i class="fas fa-chart-bar"></i> Subject Performance</h3>
    <div class="component-actions">
      <button
        class="modern-btn btn-sm btn-outline"
        onclick="refreshAnalytics('subject_performance')"
      >
        <i class="fas fa-sync-alt"></i> Refresh
      </button>
    </div>
  </div>

  <div class="component-content">
    <div id="subject-performance-container">
      <!-- Subject performance will be populated by JavaScript -->
      <div class="loading-state">
        <i class="fas fa-spinner fa-spin"></i> Loading subject analytics...
      </div>
    </div>
  </div>
</div>

<!-- Analytics Filters Component -->
<div class="analytics-filters">
  <div class="filters-header">
    <h4><i class="fas fa-filter"></i> Analytics Filters</h4>
    <button
      class="modern-btn btn-sm btn-outline"
      onclick="resetAnalyticsFilters()"
    >
      <i class="fas fa-undo"></i> Reset
    </button>
  </div>

  <div class="filters-content">
    <div class="modern-grid grid-cols-4">
      <div class="form-group">
        <label class="form-label">Term</label>
        <select
          id="analytics-term-filter"
          class="form-select"
          onchange="updateAnalytics()"
        >
          <option value="">All Terms</option>
          <!-- Terms will be populated by JavaScript -->
        </select>
      </div>

      <div class="form-group">
        <label class="form-label">Assessment Type</label>
        <select
          id="analytics-assessment-filter"
          class="form-select"
          onchange="updateAnalytics()"
        >
          <option value="">All Assessments</option>
          <!-- Assessment types will be populated by JavaScript -->
        </select>
      </div>

      <div
        class="form-group"
        id="analytics-grade-filter-group"
        style="display: none"
      >
        <label class="form-label">Grade</label>
        <select
          id="analytics-grade-filter"
          class="form-select"
          onchange="updateAnalytics()"
        >
          <option value="">All Grades</option>
          <!-- Grades will be populated by JavaScript -->
        </select>
      </div>

      <div
        class="form-group"
        id="analytics-stream-filter-group"
        style="display: none"
      >
        <label class="form-label">Stream</label>
        <select
          id="analytics-stream-filter"
          class="form-select"
          onchange="updateAnalytics()"
        >
          <option value="">All Streams</option>
          <!-- Streams will be populated by JavaScript -->
        </select>
      </div>
    </div>
  </div>
</div>

<!-- Analytics Summary Component -->
<div class="analytics-summary">
  <div class="summary-cards">
    <div class="summary-card">
      <div class="card-icon">
        <i class="fas fa-users"></i>
      </div>
      <div class="card-content">
        <div class="card-value" id="total-students-analyzed">-</div>
        <div class="card-label">Students Analyzed</div>
      </div>
    </div>

    <div class="summary-card">
      <div class="card-icon">
        <i class="fas fa-book"></i>
      </div>
      <div class="card-content">
        <div class="card-value" id="total-subjects-analyzed">-</div>
        <div class="card-label">Subjects Analyzed</div>
      </div>
    </div>

    <div class="summary-card">
      <div class="card-icon">
        <i class="fas fa-chart-line"></i>
      </div>
      <div class="card-content">
        <div class="card-value" id="top-subject-performance">-</div>
        <div class="card-label">Top Subject Avg</div>
      </div>
    </div>

    <div class="summary-card">
      <div class="card-icon">
        <i class="fas fa-medal"></i>
      </div>
      <div class="card-content">
        <div class="card-value" id="top-student-performance">-</div>
        <div class="card-label">Top Student Avg</div>
      </div>
    </div>
  </div>
</div>

<!-- No Data State Component -->
<div id="no-data-state" class="no-data-state" style="display: none">
  <div class="no-data-icon">
    <i class="fas fa-chart-line"></i>
  </div>
  <h3>No Analytics Data Available</h3>
  <p>
    There isn't enough data to generate meaningful analytics for the selected
    filters.
  </p>
  <div class="no-data-suggestions">
    <h4>Suggestions:</h4>
    <ul>
      <li>Ensure marks have been uploaded for students</li>
      <li>Try selecting a different term or assessment type</li>
      <li>Check that students are properly assigned to classes</li>
      <li>Verify that subjects have been configured correctly</li>
    </ul>
  </div>
  <button class="modern-btn btn-primary" onclick="resetAnalyticsFilters()">
    <i class="fas fa-undo"></i> Reset Filters
  </button>
</div>

<style>
  /* Analytics Dashboard Styles */
  .analytics-component {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    margin-bottom: var(--space-6);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .subject-performance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-3);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .performance-mode-toggle {
    display: flex;
    gap: var(--space-2);
  }

  .component-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-3);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .component-header h3 {
    color: var(--primary-color);
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
  }

  .component-actions {
    display: flex;
    gap: var(--space-2);
  }

  .top-performer-card {
    display: flex;
    align-items: center;
    padding: var(--space-4);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-3);
    transition: all 0.3s ease;
  }

  .top-performer-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }

  .performer-rank {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-weight: bold;
    font-size: 1.1rem;
    margin-right: var(--space-4);
  }

  .rank-1 {
    background: linear-gradient(135deg, #ffd700, #ffa500);
    color: #fff;
  }
  .rank-2 {
    background: linear-gradient(135deg, #c0c0c0, #a0a0a0);
    color: #fff;
  }
  .rank-3 {
    background: linear-gradient(135deg, #cd7f32, #b8860b);
    color: #fff;
  }
  .rank-other {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    color: #fff;
  }

  .performer-info {
    flex: 1;
  }

  .performer-name {
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--text-primary);
    margin-bottom: var(--space-1);
  }

  .performer-details {
    display: flex;
    gap: var(--space-3);
    font-size: 0.9rem;
    color: var(--text-secondary);
  }

  .performer-metrics {
    display: flex;
    align-items: center;
    gap: var(--space-3);
  }

  .performance-badge {
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
  }

  .performance-excellent {
    background: var(--green-100);
    color: var(--green-800);
  }
  .performance-very-good {
    background: var(--blue-100);
    color: var(--blue-800);
  }
  .performance-good {
    background: var(--yellow-100);
    color: var(--yellow-800);
  }
  .performance-satisfactory {
    background: var(--orange-100);
    color: var(--orange-800);
  }
  .performance-needs-improvement {
    background: var(--red-100);
    color: var(--red-800);
  }

  .subject-analytics-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-3);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-2);
  }

  .subject-name {
    font-weight: 600;
    color: var(--text-primary);
  }

  .subject-metrics {
    display: flex;
    align-items: center;
    gap: var(--space-3);
  }

  .analytics-filters {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    margin-bottom: var(--space-6);
  }

  .filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-4);
  }

  .filters-header h4 {
    color: var(--primary-color);
    margin: 0;
  }

  .analytics-summary {
    margin-bottom: var(--space-6);
  }

  .summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
  }

  .summary-card {
    display: flex;
    align-items: center;
    padding: var(--space-4);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
  }

  .card-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(
      135deg,
      var(--primary-color),
      var(--secondary-color)
    );
    color: white;
    margin-right: var(--space-3);
  }

  .card-content {
    flex: 1;
  }

  .card-value {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--text-primary);
    margin-bottom: var(--space-1);
  }

  .card-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
  }

  .no-data-state {
    text-align: center;
    padding: var(--space-8);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
  }

  .no-data-icon {
    font-size: 4rem;
    color: var(--gray-400);
    margin-bottom: var(--space-4);
  }

  .no-data-suggestions {
    text-align: left;
    max-width: 500px;
    margin: var(--space-6) auto;
    padding: var(--space-4);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-md);
  }

  .loading-state {
    text-align: center;
    padding: var(--space-6);
    color: var(--text-secondary);
  }

  .loading-state i {
    margin-right: var(--space-2);
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .modern-grid.grid-cols-4 {
      grid-template-columns: 1fr;
    }

    .summary-cards {
      grid-template-columns: 1fr;
    }

    .component-header {
      flex-direction: column;
      align-items: flex-start;
      gap: var(--space-2);
    }

    .performer-details {
      flex-direction: column;
      gap: var(--space-1);
    }
  }
</style>
