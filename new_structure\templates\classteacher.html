<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Class Teacher Dashboard - {{ school_info.school_name|default('School Management System') }}</title>

    <!-- Modern Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Modern CSS Framework -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/modern_classteacher.css') }}">

    <!-- Legacy CSS for compatibility -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/manage_pages_enhanced.css') }}">

    <script src="{{ url_for('static', filename='js/script.js') }}" defer></script>
    <style>
        .stream-status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-ready {
            background-color: #28a745;
        }
        .status-pending {
            background-color: #dc3545;
        }
        .stream-status-list {
            margin-top: 10px;
            max-height: 150px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 8px;
            border-radius: 4px;
        }
        .stream-status-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }
        .refresh-status {
            background-color: #6c757d;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            cursor: pointer;
            font-size: 12px;
            margin-top: 5px;
        }
        .marksheet-info {
            background-color: #f8f9fa;
            border-left: 4px solid #17a2b8;
            padding: 10px;
            margin: 10px 0;
            font-size: 14px;
        }

        /* New styles for improved layout */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .dashboard-full-width {
            grid-column: 1 / span 2;
        }

        .dashboard-card {
            margin-top: 0;
        }

        .tab-container {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }

        .main-tab {
            padding: 12px 24px;
            cursor: pointer;
            font-weight: 500;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }

        .main-tab.active {
            border-bottom-color: #007BFF;
            color: #007BFF;
        }

        .main-tab:hover {
            background-color: #f8f9fa;
        }

        .tab-content-container {
            display: none;
        }

        .tab-content-container.active {
            display: block;
        }

        .quick-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }

        .quick-action-btn {
            padding: 10px 15px;
            border-radius: 5px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            transition: all 0.2s;
        }

        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .action-icon {
            font-size: 18px;
        }

        .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #eee;
            padding-bottom: 8px;
        }

        /* Enhanced Dashboard Specific Styling */

        /* ENHANCED TABLE WRAPPER FOR HORIZONTAL SCROLLING */
        .table-wrapper {
            width: 100%;
            overflow-x: auto;
            overflow-y: visible;
            position: relative;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
            box-shadow: 0 0 8px rgba(0, 0, 0, 0.05);
            /* Ensure rightmost columns are fully visible */
            padding-right: 20px;
            margin-right: -20px;
            /* Smooth scrolling */
            scroll-behavior: smooth;
            /* Ensure content is not clipped */
            contain: none;
        }

        /* Add padding to ensure rightmost content is visible */
        .table-wrapper::after {
            content: "";
            display: block;
            width: 50px;
            height: 1px;
            float: right;
            clear: both;
        }

        /* Enhanced table styling for better visibility */
        .table-wrapper table {
            width: 100%;
            min-width: max-content;
            table-layout: auto;
            border-collapse: collapse;
            margin: 0;
        }

        .table-wrapper th,
        .table-wrapper td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
            vertical-align: middle;
            min-width: 120px;
            position: relative;
        }

        .table-wrapper th {
            background-color: #f8f9fa;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        /* Special styling for rightmost columns */
        .table-wrapper td:last-child,
        .table-wrapper td:nth-last-child(2),
        .table-wrapper td:nth-last-child(3) {
            background-color: #f8f9fa !important;
            border: 1px solid #dee2e6 !important;
            min-width: 120px !important;
            max-width: 150px !important;
            width: 120px !important;
            position: relative !important;
            z-index: 999 !important;
            padding: 4px !important;
            text-align: center !important;
        }

        /* Ensure rightmost column inputs are fully accessible */
        .table-wrapper td:last-child input,
        .table-wrapper td:nth-last-child(2) input,
        .table-wrapper td:nth-last-child(3) input {
            width: 100% !important;
            min-width: 80px !important;
            max-width: 120px !important;
            height: 35px !important;
            background-color: #ffffff !important;
            border: 1px solid #ced4da !important;
            border-radius: 4px !important;
            padding: 6px !important;
            margin: 0 !important;
            font-size: 14px !important;
            font-weight: normal !important;
            text-align: center !important;
            z-index: 1000 !important;
            position: relative !important;
            display: block !important;
            visibility: visible !important;
            pointer-events: auto !important;
            cursor: text !important;
            box-sizing: border-box !important;
        }

        /* Force visibility for Creative Arts column specifically */
        .table-wrapper th:last-child,
        .table-wrapper td:last-child {
            background-color: #f8f9fa !important;
            border: 1px solid #dee2e6 !important;
        }

        /* COMPREHENSIVE INPUT FIELD FIXES */
        .table-wrapper input[type="number"],
        .student-mark,
        .component-mark {
            width: 100% !important;
            min-width: 80px !important;
            max-width: 120px !important;
            height: 35px !important;
            padding: 4px !important;
            margin: 0 !important;
            border: 2px solid #007bff !important;
            border-radius: 4px !important;
            text-align: center !important;
            background-color: white !important;
            color: #333 !important;
            font-size: 14px !important;
            font-weight: bold !important;
            pointer-events: auto !important;
            cursor: text !important;
            user-select: auto !important;
            opacity: 1 !important;
            z-index: 1000 !important;
            position: relative !important;
            display: block !important;
            visibility: visible !important;
            outline: none !important;
            box-sizing: border-box !important;
        }

        /* Input focus states */
        .table-wrapper input[type="number"]:focus,
        .student-mark:focus,
        .component-mark:focus {
            border-color: #28a745 !important;
            background-color: #f8fff8 !important;
            box-shadow: 0 0 5px rgba(40, 167, 69, 0.5) !important;
            transform: scale(1.02) !important;
        }

        /* Input hover states */
        .table-wrapper input[type="number"]:hover,
        .student-mark:hover,
        .component-mark:hover {
            border-color: #17a2b8 !important;
            background-color: #f0f8ff !important;
        }

        /* Override any disabled/readonly states */
        .table-wrapper input[disabled],
        .table-wrapper input[readonly],
        .student-mark[disabled],
        .student-mark[readonly],
        .component-mark[disabled],
        .component-mark[readonly] {
            pointer-events: auto !important;
            cursor: text !important;
            opacity: 1 !important;
            background-color: white !important;
            color: #333 !important;
            border: 2px solid #e9ecef !important;
        }

        /* Animation for highlighting inputs */
        @keyframes pulse {
            0% {
                background-color: #e3f2fd !important;
                transform: scale(1);
            }
            50% {
                background-color: #bbdefb !important;
                transform: scale(1.02);
            }
            100% {
                background-color: #e3f2fd !important;
                transform: scale(1);
            }
        }

        /* Ensure Creative Arts inputs are always visible */
        .creative-arts-input {
            background-color: #ffffff !important;
            border: 3px solid #ff0000 !important;
            min-width: 130px !important;
            width: 130px !important;
            height: 40px !important;
            font-size: 16px !important;
            font-weight: bold !important;
            z-index: 1001 !important;
            position: relative !important;
            display: block !important;
            visibility: visible !important;
            pointer-events: auto !important;
        }

        /* Enhanced Sticky Navigation System */
        .enhanced-nav-system {
            position: sticky;
            top: 0;
            z-index: 1000;
            background: linear-gradient(135deg, #1f7d53 0%, #28a745 100%);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            backdrop-filter: blur(20px);
        }

        .nav-primary {
            padding: 0.75rem 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .nav-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .nav-brand {
            color: white;
            font-weight: 700;
            font-size: 1.2rem;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-brand:hover {
            color: white;
            text-decoration: none;
        }

        .nav-main {
            display: flex;
            gap: 0.5rem;
            flex: 1;
            justify-content: center;
        }

        .nav-item {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 25px;
            padding: 0.6rem 1.2rem;
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            font-size: 0.9rem;
            position: relative;
            overflow: hidden;
        }

        .nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .nav-item:hover::before {
            left: 100%;
        }

        .nav-item:hover {
            background: rgba(255,255,255,0.25);
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            color: white;
            text-decoration: none;
        }

        .nav-item.active {
            background: rgba(255,255,255,0.3);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .nav-item i {
            font-size: 1rem;
            width: 18px;
            text-align: center;
        }

        .nav-secondary {
            padding: 0.5rem 0;
            background: rgba(255,255,255,0.05);
        }

        .nav-quick-actions {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .quick-action {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.15);
            border-radius: 20px;
            padding: 0.4rem 0.8rem;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            font-size: 0.8rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }

        .quick-action:hover {
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
            transform: scale(1.05);
        }

        .quick-action i {
            font-size: 0.8rem;
        }

        /* Enhanced Floating Action System */
        .floating-action-system {
            position: fixed;
            bottom: 25px;
            right: 25px;
            z-index: 1001;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 15px;
        }

        /* Mobile-First Responsive Enhancements for Classteacher Dashboard */
        @media (max-width: 480px) {
            .modern-container {
                padding: 0.75rem;
            }

            /* Mobile Navigation */
            .enhanced-nav-system {
                position: relative;
            }

            .nav-container {
                flex-direction: column;
                gap: 0.75rem;
                padding: 0.75rem;
            }

            .nav-brand {
                font-size: 1rem;
                margin-bottom: 0.5rem;
            }

            .nav-main {
                flex-direction: column;
                width: 100%;
                gap: 0.5rem;
            }

            .nav-item {
                width: 100%;
                justify-content: center;
                padding: 0.75rem;
                border-radius: 0.5rem;
            }

            .nav-secondary {
                padding: 0.5rem 0;
            }

            .nav-quick-actions {
                flex-direction: column;
                gap: 0.5rem;
            }

            .quick-action {
                width: 100%;
                justify-content: center;
                padding: 0.5rem;
            }

            /* Mobile Dashboard Grid */
            .dashboard-grid {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }

            .dashboard-full-width {
                grid-column: 1;
            }

            /* Mobile Tab Container */
            .tab-container {
                flex-direction: column;
                gap: 0.5rem;
            }

            .main-tab {
                padding: 0.75rem;
                text-align: center;
                border-radius: 0.5rem;
                border: 1px solid #ddd;
                border-bottom: 1px solid #ddd;
                margin-bottom: 0.5rem;
            }

            .main-tab.active {
                background-color: #007BFF;
                color: white;
                border-color: #007BFF;
            }

            /* Mobile Quick Actions */
            .quick-actions {
                flex-direction: column;
                gap: 0.5rem;
                padding: 0.75rem;
            }

            .quick-action-btn {
                width: 100%;
                justify-content: center;
                padding: 0.75rem;
                font-size: 0.875rem;
            }

            /* Mobile Table Wrapper */
            .table-wrapper {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                border-radius: 0.5rem;
                margin: 0;
                padding-right: 0;
            }

            .table-wrapper table {
                min-width: 600px;
                font-size: 0.75rem;
            }

            .table-wrapper th,
            .table-wrapper td {
                padding: 0.5rem;
                min-width: 80px;
            }

            .table-wrapper input[type="number"],
            .student-mark,
            .component-mark {
                min-width: 60px !important;
                max-width: 80px !important;
                height: 30px !important;
                font-size: 12px !important;
                padding: 2px !important;
            }

            /* Mobile Cards */
            .modern-card {
                padding: 0.75rem;
                margin-bottom: 0.75rem;
            }

            .card-header {
                flex-direction: column;
                text-align: center;
                gap: 0.5rem;
            }

            .card-title {
                font-size: 1rem;
            }

            /* Mobile Section Titles */
            .section-title {
                font-size: 1rem;
                text-align: center;
            }

            /* Mobile Floating Actions */
            .floating-action-system {
                bottom: 15px;
                right: 15px;
                gap: 10px;
            }

            .fab-main {
                width: 50px;
                height: 50px;
                font-size: 1.25rem;
            }

            .fab-secondary {
                width: 45px;
                height: 45px;
                font-size: 1rem;
            }

            /* Mobile Forms */
            .form-group {
                margin-bottom: 0.75rem;
            }

            .form-control {
                padding: 0.75rem;
                font-size: 1rem; /* Prevent zoom on iOS */
            }

            .btn {
                padding: 0.75rem 1rem;
                width: 100%;
                margin-bottom: 0.5rem;
            }

            /* Mobile Modals */
            .modal-dialog {
                margin: 0.75rem;
                max-width: calc(100vw - 1.5rem);
            }

            .modal-content {
                border-radius: 0.5rem;
            }

            .modal-header {
                padding: 0.75rem;
                flex-direction: column;
                text-align: center;
            }

            .modal-body {
                padding: 0.75rem;
            }

            .modal-footer {
                padding: 0.75rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .modal-footer .btn {
                width: 100%;
            }
        }

        /* Small Mobile Devices */
        @media (max-width: 360px) {
            .modern-container {
                padding: 0.5rem;
            }

            .nav-container {
                padding: 0.5rem;
            }

            .nav-item {
                padding: 0.5rem;
                font-size: 0.8rem;
            }

            .quick-action-btn {
                padding: 0.5rem;
                font-size: 0.8rem;
            }

            .table-wrapper th,
            .table-wrapper td {
                padding: 0.25rem;
                min-width: 60px;
            }

            .table-wrapper input[type="number"],
            .student-mark,
            .component-mark {
                min-width: 50px !important;
                max-width: 60px !important;
                height: 25px !important;
                font-size: 10px !important;
            }
        }

        /* Tablet Portrait */
        @media (max-width: 768px) and (min-width: 481px) {
            .nav-main {
                flex-wrap: wrap;
                justify-content: center;
            }

            .nav-item {
                flex: 1;
                min-width: 120px;
                justify-content: center;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .quick-actions {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 0.75rem;
            }

            .tab-container {
                flex-wrap: wrap;
                justify-content: center;
            }

            .main-tab {
                flex: 1;
                min-width: 120px;
                text-align: center;
            }
        }

        /* Tablet Landscape */
        @media (max-width: 1024px) and (min-width: 769px) {
            .nav-main {
                flex-wrap: wrap;
                gap: 0.75rem;
            }

            .dashboard-grid {
                grid-template-columns: 1fr 1fr;
                gap: 1.5rem;
            }

            .quick-actions {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 1rem;
            }
        }

        .fab-main {
            width: 65px;
            height: 65px;
            border-radius: 50%;
            background: linear-gradient(135deg, #1f7d53 0%, #28a745 100%);
            color: white;
            border: none;
            font-size: 1.6rem;
            cursor: pointer;
            box-shadow: 0 8px 30px rgba(31, 125, 83, 0.4);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .fab-main::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .fab-main:hover::before {
            opacity: 1;
        }

        .fab-main:hover {
            transform: scale(1.15) rotate(90deg);
            box-shadow: 0 12px 40px rgba(31, 125, 83, 0.6);
        }

        .fab-main.active {
            transform: rotate(45deg);
            background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        }

        .fab-menu {
            display: flex;
            flex-direction: column;
            gap: 12px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(20px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .fab-menu.active {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .fab-item {
            display: flex;
            align-items: center;
            gap: 12px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 12px 18px;
            border-radius: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            text-decoration: none;
            color: #333;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            white-space: nowrap;
            font-weight: 500;
            border: 1px solid rgba(31, 125, 83, 0.1);
            min-width: 180px;
        }

        .fab-item:hover {
            background: rgba(31, 125, 83, 0.1);
            transform: translateX(-8px) scale(1.05);
            color: #1f7d53;
            text-decoration: none;
            box-shadow: 0 8px 30px rgba(31, 125, 83, 0.2);
        }

        .fab-item i {
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
        }

        /* Speed Dial Animation */
        .fab-item:nth-child(1) { transition-delay: 0.1s; }
        .fab-item:nth-child(2) { transition-delay: 0.15s; }
        .fab-item:nth-child(3) { transition-delay: 0.2s; }
        .fab-item:nth-child(4) { transition-delay: 0.25s; }
        .fab-item:nth-child(5) { transition-delay: 0.3s; }

        /* Enhanced Management Options Grid */
        .management-options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .management-option-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border: 1px solid rgba(31, 125, 83, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            color: inherit;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .management-option-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), #28a745);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .management-option-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
            text-decoration: none;
            color: inherit;
        }

        .management-option-card:hover::before {
            transform: scaleX(1);
        }

        .option-icon {
            font-size: 3rem;
            min-width: 80px;
            text-align: center;
            background: linear-gradient(135deg, rgba(31, 125, 83, 0.1) 0%, rgba(31, 125, 83, 0.2) 100%);
            border-radius: 50%;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .management-option-card:hover .option-icon {
            background: linear-gradient(135deg, var(--primary-color) 0%, #28a745 100%);
            transform: scale(1.1);
        }

        .option-content {
            flex: 1;
        }

        .option-content h4 {
            margin: 0 0 0.8rem 0;
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .option-content p {
            margin: 0 0 1rem 0;
            color: var(--text-light);
            line-height: 1.5;
        }

        .option-stats {
            display: flex;
            gap: 0.8rem;
            flex-wrap: wrap;
        }

        .stat-badge {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: var(--primary-color);
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            border: 1px solid rgba(31, 125, 83, 0.2);
        }

        .option-arrow {
            font-size: 1.5rem;
            color: var(--primary-color);
            transition: all 0.3s ease;
        }

        .management-option-card:hover .option-arrow {
            transform: translateX(5px);
        }

        /* Analytics Quick Access */
        .analytics-quick-access {
            margin-bottom: var(--space-6);
        }

        .quick-access-card {
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
            border: 1px solid rgba(99, 102, 241, 0.2);
            border-radius: var(--radius-lg);
            padding: var(--space-6);
            transition: all 0.3s ease;
        }

        .quick-access-card:hover {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.15), rgba(139, 92, 246, 0.15));
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(99, 102, 241, 0.2);
        }

        .quick-access-card .card-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            font-size: 2rem;
            margin-right: var(--space-6);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .quick-access-card .card-content h3 {
            color: var(--primary-color);
            margin-bottom: var(--space-2);
            font-size: 1.3rem;
            font-weight: 600;
        }

        .quick-access-card .card-content p {
            color: var(--text-secondary);
            margin-bottom: var(--space-4);
            line-height: 1.5;
        }

        /* Enhanced Tab Navigation */
        .tab-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin: 3rem 0 2rem 0;
            overflow: hidden;
            border: 1px solid rgba(31, 125, 83, 0.1);
        }

        .main-tab {
            padding: 1.5rem 2rem;
            cursor: pointer;
            font-weight: 600;
            border-bottom: 3px solid transparent;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: white;
            color: var(--text-dark);
            position: relative;
        }

        .main-tab.active {
            border-bottom-color: var(--primary-color);
            color: var(--primary-color);
            background: linear-gradient(135deg, rgba(31, 125, 83, 0.05) 0%, rgba(31, 125, 83, 0.1) 100%);
        }

        .main-tab:hover {
            background: linear-gradient(135deg, rgba(31, 125, 83, 0.05) 0%, rgba(31, 125, 83, 0.08) 100%);
            color: var(--primary-color);
        }

        /* Enhanced Quick Actions */
        .quick-actions {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid rgba(31, 125, 83, 0.1);
            margin-bottom: 2rem;
        }

        .quick-action-btn {
            border-radius: 10px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .quick-action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
            text-decoration: none;
        }

        /* Enhanced Alert Messages */
        .alert {
            border-radius: 12px;
            border: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin: 1.5rem 0;
            position: relative;
            overflow: hidden;
        }

        .alert::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            width: 4px;
        }

        .alert-danger::before {
            background: #dc3545;
        }

        .alert-success::before {
            background: #28a745;
        }

        .alert-info::before {
            background: #17a2b8;
        }

        /* Enhanced Filter Controls */
        .filter-controls {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
            border-radius: 12px !important;
            border: 1px solid rgba(31, 125, 83, 0.1) !important;
            margin-bottom: 1.5rem;
        }

        .filter-controls select {
            border: 2px solid #e9ecef !important;
            border-radius: 8px !important;
            transition: all 0.3s ease !important;
        }

        .filter-controls select:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(31, 125, 83, 0.1) !important;
        }

        .filter-controls button {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
            border-radius: 8px !important;
            transition: all 0.3s ease !important;
        }

        .filter-controls button:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4) !important;
        }

        /* Enhanced Table Styling */
        .table-responsive table {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .table-responsive th {
            background: linear-gradient(135deg, var(--primary-color) 0%, #2c5530 100%);
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 1.2rem 1rem;
        }

        .table-responsive td {
            padding: 1rem;
            vertical-align: middle;
        }

        .table-responsive tr:hover {
            background: linear-gradient(135deg, rgba(31, 125, 83, 0.02) 0%, rgba(31, 125, 83, 0.05) 100%);
        }

        /* Enhanced Action Buttons */
        .action-buttons .btn {
            border-radius: 8px !important;
            font-weight: 600 !important;
            text-transform: uppercase !important;
            letter-spacing: 0.3px !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2) !important;
        }

        .action-buttons .btn:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3) !important;
        }

        /* Enhanced Badge Styling */
        .badge {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
            box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3) !important;
            font-weight: 600 !important;
        }

        /* Enhanced Management Filters */
        .management-filters {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1.5rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            border: 1px solid rgba(31, 125, 83, 0.1);
        }

        .filter-group {
            display: flex;
            gap: 0.8rem;
        }

        .filter-btn {
            padding: 0.8rem 1.5rem;
            border: 2px solid #e9ecef;
            background: white;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.85rem;
        }

        .filter-btn.active,
        .filter-btn:hover {
            background: linear-gradient(135deg, var(--primary-color) 0%, #28a745 100%);
            color: white;
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(31, 125, 83, 0.3);
        }

        .search-input {
            padding: 0.8rem 1.5rem;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            min-width: 250px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(31, 125, 83, 0.1);
            transform: translateY(-2px);
        }

        /* Enhanced Section Description */
        .section-description {
            color: var(--text-light);
            margin-bottom: 2rem;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        /* Role-Based Assignment Cards */
        .assignment-card {
            background: white;
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            border: 2px solid var(--gray-200);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        .assignment-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: var(--primary-color);
        }

        .assignment-card.class-teacher {
            border-color: var(--blue-300);
            background: linear-gradient(135deg, var(--blue-50) 0%, white 100%);
        }

        .assignment-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--green-600) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .assignment-content {
            flex: 1;
        }

        .assignment-title {
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: var(--space-1);
        }

        .assignment-subtitle {
            font-size: 0.85rem;
            color: var(--gray-600);
            margin-bottom: var(--space-2);
        }

        .assignment-badge {
            display: inline-block;
            background: linear-gradient(135deg, var(--blue-500) 0%, var(--indigo-500) 100%);
            color: white;
            padding: 2px 8px;
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 600;
        }

        /* Stat Cards */
        .stat-card {
            background: white;
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            border: 1px solid var(--gray-200);
            display: flex;
            align-items: center;
            gap: var(--space-3);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--green-600) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .stat-content {
            flex: 1;
        }

        .stat-number {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--gray-800);
            line-height: 1;
        }

        .stat-label {
            font-size: 0.85rem;
            color: var(--gray-600);
            margin-top: var(--space-1);
        }

        /* Enhanced Card Actions */
        .card-actions {
            position: absolute;
            top: 2rem;
            right: 2rem;
        }

        .card-actions .btn-outline {
            background: linear-gradient(135deg, rgba(31, 125, 83, 0.1) 0%, rgba(31, 125, 83, 0.2) 100%);
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .card-actions .btn-outline:hover {
            background: linear-gradient(135deg, var(--primary-color) 0%, #28a745 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(31, 125, 83, 0.3);
            text-decoration: none;
        }

        /* Enhanced Responsive Design */
        @media (max-width: 1024px) {
            .nav-main {
                gap: 0.3rem;
            }

            .nav-item {
                padding: 0.5rem 0.8rem;
                font-size: 0.85rem;
            }

            .nav-secondary {
                padding: 0.3rem 0;
            }

            .quick-action {
                padding: 0.3rem 0.6rem;
                font-size: 0.75rem;
            }
        }

        @media (max-width: 768px) {
            .enhanced-nav-system {
                position: relative;
                top: auto;
            }

            .nav-container {
                flex-direction: column;
                gap: 1rem;
                padding: 1rem;
            }

            .nav-brand {
                font-size: 1.1rem;
            }

            .nav-main {
                flex-wrap: wrap;
                justify-content: center;
                gap: 0.5rem;
            }

            .nav-item {
                padding: 0.6rem 1rem;
                font-size: 0.9rem;
                min-width: auto;
            }

            .nav-item span {
                display: none;
            }

            .nav-secondary {
                display: none;
            }

            .management-options-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .management-option-card {
                flex-direction: column;
                text-align: center;
                gap: 1rem;
            }

            .floating-action-system {
                bottom: 20px;
                right: 20px;
            }

            .fab-main {
                width: 55px;
                height: 55px;
                font-size: 1.3rem;
            }

            .fab-item {
                min-width: 160px;
                padding: 10px 15px;
                font-size: 0.9rem;
            }
        }

        @media (max-width: 480px) {
            .nav-container {
                padding: 0.8rem;
            }

            .nav-main {
                gap: 0.3rem;
            }

            .nav-item {
                padding: 0.5rem 0.8rem;
                font-size: 0.8rem;
            }

            .fab-main {
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
            }

            .fab-item {
                min-width: 140px;
                padding: 8px 12px;
                font-size: 0.85rem;
            }
        }

            .option-icon {
                width: 60px;
                height: 60px;
                font-size: 2rem;
            }

            .tab-container {
                margin: 2rem 0 1.5rem 0;
            }

            .main-tab {
                padding: 1rem 1.5rem;
                font-size: 0.9rem;
            }

            .quick-actions {
                padding: 1rem;
            }

            .quick-action-btn {
                padding: 0.8rem 1.2rem;
                font-size: 0.85rem;
            }

            .management-filters {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }

            .search-input {
                min-width: 100%;
            }

            .card-actions {
                position: static;
                margin-top: 1rem;
                text-align: right;
            }
        }
    </style>
</head>
<body>
    <!-- Error Message Section -->
    {% if error_message %}
    <div class="alert alert-danger" style="background-color: #f8d7da; color: #721c24; padding: 10px; margin: 10px 0; border-radius: 5px;">
        <strong>Error:</strong> {{ error_message }}
    </div>
    {% endif %}

    <!-- Deletion Confirmation Message -->
    {% if confirmation_message %}
    <div class="alert alert-success" style="background-color: #d4edda; color: #155724; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 5px solid #28a745;">
        {{ confirmation_message|safe }}
    </div>
    {% endif %}

    <!-- Teacher Assignment Status -->
    {% if not has_assignments %}
    <div class="alert alert-info" style="background-color: #d1ecf1; color: #0c5460; padding: 10px; margin: 10px 0; border-radius: 5px;">
        <strong>Note:</strong> You are not currently assigned to any class stream. You can still manage students and view reports for all grades and streams.
    </div>
    {% elif stream %}
    <div class="modern-alert alert-success">
        <i class="fas fa-check-circle"></i>
        <strong>Assigned Class:</strong> Grade {{ grade }} {{ stream }}
    </div>
    {% endif %}

    <!-- Enhanced Navigation System -->
    <div class="enhanced-nav-system">
        <!-- Primary Navigation -->
        <div class="nav-primary">
            <div class="nav-container">
                <a href="#" class="nav-brand">
                    {% if school_info.logo_url and school_info.logo_url != '/static/images/default_logo.png' %}
                    <img
                        src="{{ school_info.logo_url }}"
                        alt="School Logo"
                        style="
                            width: 32px;
                            height: 32px;
                            border-radius: 50%;
                            object-fit: cover;
                            margin-right: 8px;
                        "
                    />
                    {% else %}
                    <i class="fas fa-chalkboard-teacher"></i>
                    {% endif %}
                    <span>{{ school_info.school_name or 'Hillview School' }} - Class Teacher</span>
                </a>

                <!-- Mobile Navigation Toggle -->
                <button class="mobile-nav-toggle" onclick="toggleClassteacherNav()" style="display: none;">
                    <i class="fas fa-bars"></i>
                </button>

                <nav class="nav-main" id="classteacherNav">
                    <button type="button" onclick="navigateToFeature('upload-marks')" class="nav-item" data-feature="upload-marks">
                        <i class="fas fa-upload"></i>
                        <span>Upload Marks</span>
                    </button>
                    <button type="button" onclick="navigateToFeature('recent-reports')" class="nav-item" data-feature="recent-reports">
                        <i class="fas fa-chart-line"></i>
                        <span>Recent Reports</span>
                    </button>
                    <button type="button" onclick="navigateToFeature('generate-reports')" class="nav-item" data-feature="generate-reports">
                        <i class="fas fa-file-pdf"></i>
                        <span>Generate Reports</span>
                    </button>
                    <a href="{{ url_for('classteacher.analytics_dashboard') }}" class="nav-item">
                        <i class="fas fa-chart-bar"></i>
                        <span>Analytics</span>
                    </a>
                    <button type="button" onclick="navigateToFeature('management')" class="nav-item" data-feature="management">
                        <i class="fas fa-cogs"></i>
                        <span>Management</span>
                    </button>
                </nav>

                <a href="{{ url_for('auth.logout_route') }}" class="nav-item" style="background: rgba(220, 53, 69, 0.2); border-color: rgba(220, 53, 69, 0.3);">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </div>

        <!-- Secondary Quick Actions -->
        <div class="nav-secondary">
            <div class="nav-container">
                <div class="nav-quick-actions">
                    <a href="{{ url_for('classteacher.collaborative_marks_dashboard') }}" class="quick-action">
                        <i class="fas fa-users"></i>
                        <span>Collaborative</span>
                    </a>
                    <a href="{{ url_for('classteacher.view_all_reports') }}" class="quick-action">
                        <i class="fas fa-list"></i>
                        <span>All Reports</span>
                    </a>
                    <a href="{{ url_for('classteacher.grade_reports_dashboard') }}" class="quick-action">
                        <i class="fas fa-layer-group"></i>
                        <span>Grade Reports</span>
                    </a>
                    <a href="#" onclick="downloadTemplate()" class="quick-action">
                        <i class="fas fa-download"></i>
                        <span>Template</span>
                    </a>
                    <a href="{{ url_for('staff.management') }}" class="quick-action">
                        <i class="fas fa-user-tie"></i>
                        <span>Staff</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="modern-container">
        <!-- Modern Header -->
        <header class="modern-header fade-in">
            <div class="header-content">
                <div>
                    <h1 class="header-title">
                        <i class="fas fa-chalkboard-teacher"></i>
                        Class Teacher Dashboard
                    </h1>
                    <p class="header-subtitle">
                        Welcome back, <strong>{{ session.username }}</strong>!
                        <span class="modern-badge badge-info">
                            <i class="fas fa-user-tie"></i>
                            {{ session.role.replace('_', ' ').title() }}
                        </span>
                    </p>
                </div>
                <div class="header-actions">
                    <nav class="modern-nav">
                        <a href="{{ url_for('classteacher.analytics_dashboard') }}" class="nav-link">
                            <i class="fas fa-chart-line"></i>
                            Analytics
                        </a>
                        <a href="{{ url_for('classteacher.teacher_management_hub') }}" class="nav-link">
                            <i class="fas fa-users-cog"></i>
                            Teacher Hub
                        </a>
                        <a href="{{ url_for('auth.logout_route') }}" class="nav-link">
                            <i class="fas fa-sign-out-alt"></i>
                            Logout
                        </a>
                    </nav>
                </div>
            </div>
        </header>

        <!-- Role-Based Assignments Section -->
        {% if assignment_summary and assignment_summary.teacher %}
        <div class="modern-card slide-up" style="margin-bottom: var(--space-6);">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-user-graduate card-icon"></i>
                    My Teaching Assignments
                </h2>
                <div class="card-actions">
                    <span class="modern-badge badge-info">{{ assignment_summary.role.title() }}</span>
                </div>
            </div>
            <div style="padding: var(--space-6);">
                <!-- Assignment Summary Stats -->
                <div class="modern-grid grid-cols-4" style="margin-bottom: var(--space-6);">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">{{ total_subjects_taught }}</div>
                            <div class="stat-label">Subjects Taught</div>
                        </div>
                    </div>
                    {% if can_manage_classes %}
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chalkboard"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">{{ assignment_summary.total_classes_managed }}</div>
                            <div class="stat-label">Classes Managed</div>
                        </div>
                    </div>
                    {% endif %}
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">{{ assignment_summary.grades_involved|length if assignment_summary.grades_involved else 0 }}</div>
                            <div class="stat-label">Grades Involved</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-stream"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">{{ assignment_summary.streams_involved|length if assignment_summary.streams_involved else 0 }}</div>
                            <div class="stat-label">Streams Involved</div>
                        </div>
                    </div>
                </div>

                <!-- Class Teacher Assignments (if any) -->
                {% if can_manage_classes and class_teacher_assignments %}
                <div style="margin-bottom: var(--space-6);">
                    <h3 style="color: var(--primary-color); margin-bottom: var(--space-4); font-size: 1.1rem; font-weight: 600;">
                        <i class="fas fa-chalkboard-teacher"></i> Class Teacher Responsibilities
                    </h3>
                    <div class="modern-grid grid-cols-3">
                        {% for assignment in class_teacher_assignments %}
                        <div class="assignment-card class-teacher">
                            <div class="assignment-icon">
                                <i class="fas fa-chalkboard"></i>
                            </div>
                            <div class="assignment-content">
                                <div class="assignment-title">{{ assignment.grade_level }}</div>
                                {% if assignment.stream_name %}
                                <div class="assignment-subtitle">Stream {{ assignment.stream_name }}</div>
                                {% endif %}
                                <div class="assignment-badge">Class Teacher</div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Analytics Quick Access -->
                <div class="analytics-quick-access">
                    <div class="quick-access-card">
                        <div class="card-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="card-content">
                            <h3>Academic Performance Analytics</h3>
                            <p>View comprehensive insights into student performance and subject analytics for your assigned classes.</p>
                            <a href="{{ url_for('classteacher.analytics_dashboard') }}" class="modern-btn btn-primary">
                                <i class="fas fa-chart-bar"></i> View Analytics Dashboard
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Subject Assignments with Pagination and Filtering -->
                {% if subject_assignments %}
                <div>
                    <h3 style="color: var(--primary-color); margin-bottom: var(--space-4); font-size: 1.1rem; font-weight: 600;">
                        <i class="fas fa-book-open"></i> Subject Teaching Assignments
                        <span class="modern-badge badge-info" style="font-size: 0.8rem; margin-left: var(--space-2);">
                            {{ subject_assignments|length }} Total
                        </span>
                    </h3>

                    <!-- Assignment Filters -->
                    <div style="background: var(--gray-50); padding: var(--space-4); border-radius: var(--radius-lg); margin-bottom: var(--space-4);">
                        <div class="modern-grid grid-cols-4">
                            <div class="form-group">
                                <label class="form-label" style="font-size: 0.8rem;">Search Subject</label>
                                <input type="text" id="assignment-search" placeholder="Search subjects..."
                                       class="form-input" style="font-size: 0.9rem; padding: var(--space-2);"
                                       onkeyup="filterAssignments()">
                            </div>
                            <div class="form-group">
                                <label class="form-label" style="font-size: 0.8rem;">Filter by Grade</label>
                                <select id="assignment-grade-filter" class="form-select" style="font-size: 0.9rem; padding: var(--space-2);"
                                        onchange="filterAssignments()">
                                    <option value="">All Grades</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label" style="font-size: 0.8rem;">Filter by Role</label>
                                <select id="assignment-role-filter" class="form-select" style="font-size: 0.9rem; padding: var(--space-2);"
                                        onchange="filterAssignments()">
                                    <option value="">All Roles</option>
                                    <option value="class_teacher">Class Teacher</option>
                                    <option value="subject_teacher">Subject Teacher</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label" style="font-size: 0.8rem;">Items per Page</label>
                                <select id="assignment-per-page" class="form-select" style="font-size: 0.9rem; padding: var(--space-2);"
                                        onchange="updateAssignmentPagination()">
                                    <option value="10">10 per page</option>
                                    <option value="20" selected>20 per page</option>
                                    <option value="50">50 per page</option>
                                    <option value="all">Show All</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Assignment Table -->
                    <div style="border: 1px solid var(--gray-200); border-radius: var(--radius-lg); overflow: hidden;">
                        <div class="modern-table-container" style="max-height: 400px; overflow-y: auto;">
                            <table class="modern-table" id="assignments-table">
                                <thead style="position: sticky; top: 0; background: white; z-index: 10;">
                                    <tr>
                                        <th style="cursor: pointer;" onclick="sortAssignments('subject')">
                                            <i class="fas fa-book"></i> Subject
                                            <i class="fas fa-sort" id="sort-subject"></i>
                                        </th>
                                        <th style="cursor: pointer;" onclick="sortAssignments('grade')">
                                            <i class="fas fa-layer-group"></i> Grade
                                            <i class="fas fa-sort" id="sort-grade"></i>
                                        </th>
                                        <th><i class="fas fa-stream"></i> Stream</th>
                                        <th style="cursor: pointer;" onclick="sortAssignments('role')">
                                            <i class="fas fa-user-tag"></i> Role
                                            <i class="fas fa-sort" id="sort-role"></i>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody id="assignments-tbody">
                                    <!-- Assignments will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Pagination Controls -->
                    <div id="assignment-pagination" style="display: flex; justify-content: space-between; align-items: center; margin-top: var(--space-4); padding: var(--space-4); background: var(--gray-50); border-radius: var(--radius-lg);">
                        <div id="assignment-info" style="color: var(--gray-600); font-size: 0.9rem;"></div>
                        <div id="assignment-controls" style="display: flex; gap: var(--space-2);"></div>
                    </div>
                </div>
                {% endif %}

                <!-- Quick Access Note -->
                <div style="margin-top: var(--space-6); padding: var(--space-4); background: linear-gradient(135deg, var(--blue-50) 0%, var(--indigo-50) 100%); border-radius: var(--radius-lg); border: 1px solid var(--blue-200);">
                    <div style="font-size: 0.9rem; color: var(--gray-600);">
                        <strong style="color: var(--blue-600);"><i class="fas fa-info-circle"></i> Access Control:</strong>
                        {% if assignment_summary.role == 'teacher' %}
                        As a subject teacher, you can upload marks for your assigned subjects only.
                        {% elif assignment_summary.role == 'classteacher' %}
                        As a class teacher, you can upload marks for all your subjects and manage your assigned classes.
                        {% elif assignment_summary.role == 'headteacher' %}
                        As a headteacher, you have full access to all subjects and classes in the system.
                        {% endif %}
                        The forms below are filtered to show only your accessible options.
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Permission Status Widget -->
        <div class="modern-card slide-up" id="permission-status-widget" style="margin-bottom: var(--space-6);">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-key card-icon"></i>
                    Class Permissions
                </h2>
                <div class="card-actions">
                    <button onclick="refreshPermissionStatus()" class="modern-btn btn-outline" style="font-size: 0.8rem; padding: var(--space-2) var(--space-3);">
                        <i class="fas fa-sync-alt"></i>
                        Refresh
                    </button>
                </div>
            </div>
            <div id="permission-status-content" style="padding: var(--space-6);">
                <div style="text-align: center; color: var(--gray-500);">
                    <i class="fas fa-spinner fa-spin" style="font-size: 1.5rem; margin-bottom: var(--space-2);"></i>
                    <p>Loading permission status...</p>
                </div>
            </div>
        </div>

        <!-- Modern Management Options Section -->
        <div id="management-section" class="modern-card slide-up">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-cogs card-icon"></i>
                    Management Hub
                </h2>
                <div class="card-actions">
                    <span class="modern-badge badge-info">{{ (total_students or 0) + (total_teachers or 0) + (total_subjects or 0) }} Items</span>
                </div>
            </div>

            <div class="management-filters" style="margin-bottom: var(--space-6);">
                <div class="modern-nav" style="margin-bottom: var(--space-4);">
                    <button class="nav-link active" data-filter="all">All</button>
                    <button class="nav-link" data-filter="core">Core</button>
                    <button class="nav-link" data-filter="assignments">Assignments</button>
                    <button class="nav-link" data-filter="structure">Structure</button>
                </div>
                <div class="form-group">
                    <input type="text" id="management-search" placeholder="Search management options..." class="form-input">
                </div>
            </div>
            <div class="modern-grid grid-cols-3">
                <a href="{{ url_for('classteacher.manage_students') }}" class="quick-action-card" data-category="core">
                    <div class="quick-action-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="quick-action-title">Manage Students</h3>
                    <p class="quick-action-desc">Add, edit, and organize student records</p>
                    <div class="modern-badge badge-success">{{ total_students or 0 }} Students</div>
                </a>

                <a href="{{ url_for('classteacher.manage_subjects') }}" class="quick-action-card" data-category="core">
                    <div class="quick-action-icon">
                        <i class="fas fa-book"></i>
                    </div>
                    <h3 class="quick-action-title">Manage Subjects</h3>
                    <p class="quick-action-desc">Configure subjects and curriculum</p>
                    <div class="modern-badge badge-warning">{{ total_subjects or 0 }} Subjects</div>
                </a>

                <a href="{{ url_for('classteacher.teacher_management_hub') }}" class="quick-action-card" data-category="core">
                    <div class="quick-action-icon">
                        <i class="fas fa-chalkboard-teacher"></i>
                    </div>
                    <h3 class="quick-action-title">Teacher Hub</h3>
                    <p class="quick-action-desc">Comprehensive teacher and assignment management</p>
                    <div class="modern-badge badge-info">{{ total_teachers or 0 }} Teachers</div>
                </a>

                <a href="{{ url_for('staff.management') }}" class="quick-action-card" data-category="core">
                    <div class="quick-action-icon">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <h3 class="quick-action-title">Staff Management</h3>
                    <p class="quick-action-desc">Dynamic staff assignments and professional profiles</p>
                    <div class="modern-badge badge-success">Enhanced</div>
                </a>

                <a href="{{ url_for('classteacher.manage_grades_streams') }}" class="quick-action-card" data-category="structure">
                    <div class="quick-action-icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <h3 class="quick-action-title">Grades & Streams</h3>
                    <p class="quick-action-desc">Configure grade levels and class streams</p>
                    <div class="modern-badge badge-info">{{ total_grades or 0 }} Grades</div>
                </a>

                <a href="{{ url_for('classteacher.manage_terms_assessments') }}" class="quick-action-card" data-category="structure">
                    <div class="quick-action-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <h3 class="quick-action-title">Terms & Assessments</h3>
                    <p class="quick-action-desc">Manage academic terms and assessment types</p>
                    <div class="modern-badge badge-warning">Academic Setup</div>
                </a>

                <a href="{{ url_for('classteacher.report_configuration') }}" class="quick-action-card" data-category="core">
                    <div class="quick-action-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <h3 class="quick-action-title">Report Configuration</h3>
                    <p class="quick-action-desc">Configure staff assignments, term dates, and report settings</p>
                    <div class="modern-badge badge-primary">Dynamic Reports</div>
                </a>
            </div>
        </div>

        <!-- Modern Tabs Navigation -->
        <div class="modern-tabs slide-up">
            <div class="tab-nav">
                <button class="tab-button {% if active_tab == 'recent-reports' %}active{% endif %}" onclick="switchMainTab('recent-reports')">
                    <i class="fas fa-chart-line"></i>
                    Recent Reports
                </button>
                <button class="tab-button {% if active_tab == 'upload-marks' %}active{% endif %}" onclick="switchMainTab('upload-marks')">
                    <i class="fas fa-upload"></i>
                    Upload Marks
                </button>
                <button class="tab-button {% if active_tab == 'generate-marksheet' %}active{% endif %}" onclick="switchMainTab('generate-marksheet')">
                    <i class="fas fa-file-pdf"></i>
                    Generate Reports
                </button>
            </div>
        </div>

        <!-- Modern Quick Actions -->
        <div class="modern-quick-actions slide-up">
            <a href="{{ url_for('classteacher.view_all_reports') }}" class="quick-action-card">
                <div class="quick-action-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <h3 class="quick-action-title">View All Reports</h3>
                <p class="quick-action-desc">Browse and manage all generated reports</p>
            </a>

            <a href="{{ url_for('classteacher.collaborative_marks_dashboard') }}" class="quick-action-card">
                <div class="quick-action-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3 class="quick-action-title">Collaborative Marks</h3>
                <p class="quick-action-desc">Multi-teacher marks upload system</p>
            </a>

            <a href="{{ url_for('classteacher.grade_reports_dashboard') }}" class="quick-action-card">
                <div class="quick-action-icon">
                    <i class="fas fa-layer-group"></i>
                </div>
                <h3 class="quick-action-title">Grade Reports</h3>
                <p class="quick-action-desc">Multi-stream grade analysis</p>
            </a>

            <a href="#" onclick="switchMainTab('upload-marks')" class="quick-action-card">
                <div class="quick-action-icon">
                    <i class="fas fa-plus-circle"></i>
                </div>
                <h3 class="quick-action-title">Upload Marks</h3>
                <p class="quick-action-desc">Add new student marks</p>
            </a>

            <a href="#" onclick="switchMainTab('generate-marksheet')" class="quick-action-card">
                <div class="quick-action-icon">
                    <i class="fas fa-file-export"></i>
                </div>
                <h3 class="quick-action-title">Generate Reports</h3>
                <p class="quick-action-desc">Create class marksheets</p>
            </a>

            <a href="#" onclick="downloadTemplate()" class="quick-action-card">
                <div class="quick-action-icon">
                    <i class="fas fa-download"></i>
                </div>
                <h3 class="quick-action-title">Download Template</h3>
                <p class="quick-action-desc">Get marks upload template</p>
            </a>
        </div>

        <!-- Modern Recent Reports Tab Content -->
        <div id="recent-reports-tab" class="tab-content-container {% if active_tab == 'recent-reports' %}active{% endif %}">
            <div class="modern-card slide-up">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-chart-line card-icon"></i>
                        Recent Reports
                    </h2>
                    <div class="card-actions">
                        <a href="{{ url_for('classteacher.view_all_reports') }}" class="modern-btn btn-outline">
                            <i class="fas fa-external-link-alt"></i>
                            View All
                        </a>
                    </div>
                </div>

                <!-- Modern Filter Controls -->
                <div class="modern-form" style="background: var(--gray-50); padding: var(--space-6); border-radius: var(--radius-lg); margin-bottom: var(--space-6);">
                    <div class="modern-grid grid-cols-4">
                        <div class="form-group">
                            <label for="sort-reports" class="form-label">Sort by</label>
                            <select id="sort-reports" onchange="sortReports(this.value)" class="form-select">
                                <option value="date" {% if request.args.get('sort') == 'date' or not request.args.get('sort') %}selected{% endif %}>Date (newest first)</option>
                                <option value="grade" {% if request.args.get('sort') == 'grade' %}selected{% endif %}>Grade</option>
                                <option value="term" {% if request.args.get('sort') == 'term' %}selected{% endif %}>Term</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="filter-grade" class="form-label">Grade</label>
                            <select id="filter-grade" onchange="filterReports()" class="form-select">
                                <option value="">All Grades</option>
                                {% for grade_option in grades %}
                                <option value="{{ grade_option }}" {% if request.args.get('filter_grade') == grade_option %}selected{% endif %}>{{ grade_option }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="filter-term" class="form-label">Term</label>
                            <select id="filter-term" onchange="filterReports()" class="form-select">
                                <option value="">All Terms</option>
                                {% for term_option in terms %}
                                <option value="{{ term_option }}" {% if request.args.get('filter_term') == term_option %}selected{% endif %}>{{ term_option }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">&nbsp;</label>
                            <button onclick="resetFilters()" class="modern-btn btn-outline">
                                <i class="fas fa-undo"></i>
                                Reset Filters
                            </button>
                        </div>
                    </div>
                </div>

                <div class="modern-table-container">
                    <table class="modern-table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-hashtag"></i> ID</th>
                                <th><i class="fas fa-layer-group"></i> Grade</th>
                                <th><i class="fas fa-stream"></i> Stream</th>
                                <th><i class="fas fa-calendar"></i> Term</th>
                                <th><i class="fas fa-clipboard-check"></i> Assessment</th>
                                <th><i class="fas fa-clock"></i> Date</th>
                                <th><i class="fas fa-chart-bar"></i> Marks</th>
                                <th><i class="fas fa-cogs"></i> Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if recent_reports %}
                            {% for report in recent_reports %}
                            <tr>
                                <td><strong>{{ report.id }}</strong></td>
                                <td>
                                    <span class="modern-badge badge-info">{{ report.grade }}</span>
                                </td>
                                <td>
                                    <span class="modern-badge badge-success">{{ report.stream }}</span>
                                </td>
                                <td>{{ report.term.replace('_', ' ').title() }}</td>
                                <td>{{ report.assessment_type.replace('_', ' ').title() }}</td>
                                <td>{{ report.date }}</td>
                                <td>
                                    <span class="modern-badge badge-warning">
                                        <i class="fas fa-chart-line"></i>
                                        {{ report.mark_count }} marks
                                    </span>
                                </td>
                                <td>
                                    <div style="display: flex; gap: var(--space-2); flex-wrap: wrap;">
                                        <a href="{{ url_for('classteacher.edit_class_marks', grade=report.grade, stream=report.stream, term=report.term, assessment_type=report.assessment_type) }}"
                                           class="modern-btn btn-primary" style="font-size: 0.75rem; padding: var(--space-2) var(--space-3);" title="Edit marks">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ url_for('classteacher.preview_class_report', grade=report.grade, stream=report.stream, term=report.term, assessment_type=report.assessment_type) }}"
                                           class="modern-btn btn-success" style="font-size: 0.75rem; padding: var(--space-2) var(--space-3);" title="View report">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('classteacher.preview_class_report', grade=report.grade, stream=report.stream, term=report.term, assessment_type=report.assessment_type) }}"
                                           class="modern-btn btn-warning" style="font-size: 0.75rem; padding: var(--space-2) var(--space-3);" title="Print report">
                                            <i class="fas fa-print"></i>
                                        </a>
                                        <button onclick="confirmDeleteReport('{{ report.grade }}', '{{ report.stream }}', '{{ report.term }}', '{{ report.assessment_type }}')"
                                                class="modern-btn btn-danger" style="font-size: 0.75rem; padding: var(--space-2) var(--space-3);" title="Delete report">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                            {% else %}
                            <tr>
                                <td colspan="8" style="text-align: center; padding: var(--space-16);">
                                    <div style="display: flex; flex-direction: column; align-items: center; gap: var(--space-4);">
                                        <i class="fas fa-chart-line" style="font-size: 3rem; color: var(--gray-400);"></i>
                                        <h3 style="margin: 0; color: var(--gray-600);">No reports available</h3>
                                        <p style="margin: 0; color: var(--gray-500);">Upload marks to generate reports</p>
                                        <a href="#" onclick="switchMainTab('upload-marks')" class="modern-btn btn-primary">
                                            <i class="fas fa-plus"></i>
                                            Upload Marks
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Modern Upload Marks Tab Content -->
        <div id="upload-marks-tab" class="tab-content-container {% if active_tab == 'upload-marks' %}active{% endif %}">
            <div id="upload-marks-section" class="modern-card slide-up">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-upload card-icon"></i>
                        Upload Class Marks
                    </h2>
                    <div class="card-actions">
                        <span class="modern-badge badge-info">All Subjects</span>
                    </div>
                </div>

            {% if not show_students %}
            <!-- Modern Upload Method Selection -->
            <div class="modern-tabs">
                <div class="tab-nav">
                    <button class="tab-button active" data-tab="manual-entry">
                        <i class="fas fa-keyboard"></i>
                        Manual Entry
                    </button>
                    <button class="tab-button" data-tab="bulk-upload">
                        <i class="fas fa-file-upload"></i>
                        Bulk Upload
                    </button>
                </div>
                <div class="tab-content">
                    <div class="tab-pane active" id="manual-entry">
                        <form id="upload-form" method="POST" action="{{ url_for('classteacher.dashboard') }}" class="modern-form">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>

                            <div class="modern-grid grid-cols-2">
                                <div class="form-group">
                                    <label for="education_level" class="form-label">Education Level</label>
                                    <select id="education_level" name="education_level" required onchange="updateSubjects()" class="form-select">
                                        <option value="">Select Education Level</option>
                                        <option value="lower_primary" {% if education_level == 'lower_primary' %}selected{% endif %}>Lower Primary</option>
                                        <option value="upper_primary" {% if education_level == 'upper_primary' %}selected{% endif %}>Upper Primary</option>
                                        <option value="junior_secondary" {% if education_level == 'junior_secondary' %}selected{% endif %}>Junior Secondary</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="term" class="form-label">Term</label>
                                    <select id="term" name="term" required class="form-select">
                                        <option value="">Select Term</option>
                                        {% for term_option in terms %}
                                        <option value="{{ term_option }}" {% if term == term_option %}selected{% endif %}>{{ term_option.replace('_', ' ').title() }}</option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="assessment_type" class="form-label">Assessment Type</label>
                                    <select id="assessment_type" name="assessment_type" required class="form-select">
                                        <option value="">Select Assessment Type</option>
                                        {% for assessment_option in assessment_types %}
                                        <option value="{{ assessment_option }}" {% if assessment_type == assessment_option %}selected{% endif %}>{{ assessment_option.replace('_', ' ').title() }}</option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="grade" class="form-label">Grade</label>
                                    <select id="grade" name="grade" required onchange="fetchStreams()" class="form-select">
                                        <option value="">Select Grade</option>
                                        {% for grade_option in grades %}
                                        <option value="{{ grade_option }}" {% if grade == grade_option %}selected{% endif %}>{{ grade_option }}</option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="stream" class="form-label">Stream</label>
                                    <select id="stream" name="stream" required class="form-select">
                                        <option value="">Select Stream</option>
                                        <!-- Streams will be populated dynamically via JavaScript -->
                                    </select>
                                </div>
                            </div>

                            <!-- Hidden field for total marks -->
                            <input type="hidden" id="total_marks" name="total_marks" value="100">

                            <div style="text-align: center; margin-top: var(--space-6);">
                                <button type="submit" name="upload_marks" value="1" class="modern-btn btn-primary" id="upload-btn">
                                    <i class="fas fa-users"></i>
                                    Load Students & Subjects
                                </button>
                            </div>
                        </form>
                    </div>
                    <div class="tab-pane" id="bulk-upload">
                        <form id="bulk-upload-form" method="POST" action="{{ url_for('classteacher.dashboard') }}" class="modern-form" enctype="multipart/form-data">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <div class="form-group">
                                <label for="bulk_education_level">Education Level</label>
                                <select id="bulk_education_level" name="education_level" required onchange="updateBulkGrades()">
                                    <option value="">Select Education Level</option>
                                    <option value="lower_primary" {% if education_level == 'lower_primary' %}selected{% endif %}>Lower Primary</option>
                                    <option value="upper_primary" {% if education_level == 'upper_primary' %}selected{% endif %}>Upper Primary</option>
                                    <option value="junior_secondary" {% if education_level == 'junior_secondary' %}selected{% endif %}>Junior Secondary</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="bulk_term">Term</label>
                                <select id="bulk_term" name="term" required>
                                    <option value="">Select Term</option>
                                    {% for term_option in terms %}
                                    <option value="{{ term_option }}" {% if term == term_option %}selected{% endif %}>{{ term_option }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="bulk_assessment_type">Assessment Type</label>
                                <select id="bulk_assessment_type" name="assessment_type" required>
                                    <option value="">Select Assessment Type</option>
                                    {% for assessment_option in assessment_types %}
                                    <option value="{{ assessment_option }}" {% if assessment_type == assessment_option %}selected{% endif %}>{{ assessment_option }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- Total Marks field removed -->
                            <input type="hidden" id="bulk_total_marks" name="total_marks" value="100">

                            <div class="form-group">
                                <label for="bulk_grade">Grade</label>
                                <select id="bulk_grade" name="grade" required onchange="fetchBulkStreams()">
                                    <option value="">Select Grade</option>
                                    {% for grade_option in grades %}
                                    <option value="{{ grade_option }}" {% if grade == grade_option %}selected{% endif %}>{{ grade_option }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="bulk_stream">Stream</label>
                                <select id="bulk_stream" name="stream" required>
                                    <option value="">Select Stream</option>
                                    <!-- Streams will be populated dynamically via JavaScript -->
                                </select>
                            </div>

                            <div class="form-group" style="grid-column: span 2;">
                                <label for="marks_file">Upload Marks File (Excel or CSV)</label>
                                <input type="file" id="marks_file" name="marks_file" accept=".xlsx,.xls,.csv" required>
                                <p class="help-text">Upload an Excel or CSV file with student marks. The file should have student names or admission numbers as rows and subjects as columns.</p>
                            </div>

                            <div class="form-group" style="grid-column: span 2; text-align: center;">
                                <a href="#" class="btn-outline" onclick="downloadTemplate()" style="margin-right: 10px;">
                                    Download Template
                                </a>
                                <button type="submit" name="bulk_upload_marks" value="1" class="btn" id="bulk-upload-btn">
                                    Upload Marks
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {% else %}
            <!-- Form for entering student marks for all subjects -->
            <div id="pupils-list" class="pupils-list" style="grid-column: span 2;">
                <h3>Enter Marks for Grade {{ grade }} Stream {{ stream }} - All Subjects</h3>

                <form id="marks-form" method="POST" action="{{ url_for('classteacher.dashboard') }}">
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <!-- Keep existing form fields as hidden fields -->
                    <input type="hidden" name="education_level" value="{{ education_level }}">
                    <input type="hidden" name="grade" value="{{ grade }}">
                    <input type="hidden" name="stream" value="{{ stream }}">
                    <input type="hidden" name="term" value="{{ term }}">
                    <input type="hidden" name="assessment_type" value="{{ assessment_type }}">
                    <input type="hidden" name="total_marks" value="{{ total_marks }}">

                    <!-- Subject Selection and Total Marks Configuration Section -->
                    <div class="subject-marks-config">
                        <h4>Select Subjects and Set Maximum Raw Marks</h4>
                        <p class="help-text">Select which subjects to include in the report and enter the maximum possible raw marks for each subject.</p>

                        <div class="subject-selection-header">
                            <h5>Subject Selection</h5>
                            <div class="selection-controls">
                                <button type="button" class="btn-sm" onclick="selectAllSubjects()">Select All</button>
                                <button type="button" class="btn-sm" onclick="deselectAllSubjects()">Deselect All</button>
                            </div>
                        </div>
                        <p class="help-text">Check the subjects you want to include in the report. Unchecked subjects will not appear in the final report.</p>

                        <div class="subject-marks-grid">
                            {% for subject in subjects %}
                            <div class="subject-mark-item {% if subject.is_composite %}composite-subject-item{% endif %}">
                                <div class="subject-selection">
                                    <input
                                        type="checkbox"
                                        id="include_subject_{{ loop.index0 }}"
                                        name="include_subject_{{ loop.index0 }}"
                                        value="{{ subject.id }}"
                                        checked
                                        class="subject-checkbox"
                                        data-subject-id="{{ subject.id }}"
                                        data-is-composite="{{ 'true' if subject.is_composite else 'false' }}"
                                        onchange="toggleSubjectVisibility(this, '{{ subject.id }}')"
                                    >
                                    <label for="include_subject_{{ loop.index0 }}" class="checkbox-label">{{ subject.name }}</label>
                                </div>

                                {% if subject.is_composite %}
                                <div class="composite-marks-container">
                                    <div class="composite-marks-header">
                                        <span>Component Distribution</span>
                                        <small>Total: <span id="total_composite_{{ loop.index0 }}">{{ total_marks }}</span></small>
                                    </div>

                                    {% set subject_index = loop.index0 %}
                                    {% set components = subject.get_components() %}
                                    <div class="component-grid">
                                        {% for component in components %}
                                        <div class="component-marks-row">
                                            <label for="component_marks_{{ subject_index }}_{{ component.id }}" class="component-label">{{ component.name }}:</label>
                                            <div class="component-marks-input">
                                                <input
                                                    type="number"
                                                    id="component_marks_{{ subject_index }}_{{ component.id }}"
                                                    name="component_max_marks_{{ subject_index }}_{{ component.id }}"
                                                    value="{{ component.max_raw_mark }}"
                                                    min="1"
                                                    max="100"
                                                    class="component-max-marks"
                                                    data-subject="{{ subject.id }}"
                                                    data-component="{{ component.id }}"
                                                    data-subject-index="{{ subject_index }}"
                                                    data-component-weight="{{ component.weight }}"
                                                    onchange="validateMaxMarks(this)"
                                                    oninput="validateMaxMarks(this)"
                                                    title="Maximum raw marks for {{ component.name }} component (Max: 100)"
                                                    onchange="updateComponentMaxMarks('{{ subject.id }}', {{ subject_index }})"
                                                >
                                                <span class="component-weight">{{ component.name }}</span>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>

                                    <input
                                        type="hidden"
                                        id="total_marks_{{ loop.index0 }}"
                                        name="total_marks_{{ loop.index0 }}"
                                        value="{{ total_marks }}"
                                        data-subject="{{ subject.id }}"
                                    >
                                </div>
                                {% else %}
                                <div class="marks-input-container">
                                    <label for="total_marks_{{ loop.index0 }}" class="marks-label">Max Marks:</label>
                                    <input
                                        type="number"
                                        id="total_marks_{{ loop.index0 }}"
                                        name="total_marks_{{ loop.index0 }}"
                                        value="{{ total_marks }}"
                                        min="1"
                                        max="100"
                                        class="subject-total-marks"
                                        data-subject="{{ subject.id }}"
                                        onchange="updateAllMarksForSubject('{{ subject.id }}', {{ loop.index0 }}); validateMaxMarks(this)"
                                        oninput="validateMaxMarks(this)"
                                        title="Maximum raw marks for this subject (Max: 100)"
                                    >
                                </div>
                                {% endif %}
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <div class="table-wrapper">
                        <table>
                            <thead>
                                <tr>
                                    <th>Student Name</th>
                                    {% for subject in subjects %}
                                    <th class="{% if subject.is_composite %}composite-subject-header{% endif %}" data-subject-id="{{ subject.id }}">
                                        {{ subject.name }}
                                        <div style="font-size: 12px; font-weight: normal; margin-top: 5px;">
                                            <span class="subject-info" id="subject_info_{{ loop.index0 }}">Max Raw: {{ total_marks }}</span>
                                        </div>
                                        {% if subject.is_composite %}
                                        {% set components = subject.get_components() %}
                                        <div class="component-headers">
                                            {% for component in components %}
                                            <div class="component-header-item">
                                                {{ component.name }}
                                            </div>
                                            {% endfor %}
                                            <div class="component-header-item overall-header">Overall</div>
                                        </div>
                                        {% endif %}
                                    </th>
                                    {% endfor %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for student in students %}
                                <tr>
                                    <td>{{ student.name }}</td>
                                    {% for subject in subjects %}
                                    <td data-subject-id="{{ subject.id }}">
                                        {% if subject.is_composite %}
                                        <div class="composite-subject-entry-improved">
                                            {% set subject_index = loop.index0 %}
                                            {% set components = subject.get_components() %}

                                            <div class="composite-components-grid">
                                                {% for component in components %}
                                                <div class="component-column">
                                                    <div class="mark-input-container">
                                                        <input type="number"
                                                            name="component_mark_{{ student.name.replace(' ', '_') }}_{{ subject.id }}_{{ component.id }}"
                                                            value="0"
                                                            min="0"
                                                            max="{{ component.max_raw_mark or total_marks }}"
                                                            required
                                                            class="student-mark component-mark"
                                                            data-student="{{ student.name.replace(' ', '_') }}"
                                                            data-subject="{{ subject.id }}"
                                                            data-component="{{ component.id }}"
                                                            data-component-weight="{{ component.weight }}"
                                                            data-subject-index="{{ subject_index }}"
                                                            data-max-mark="{{ component.max_raw_mark or total_marks }}"
                                                            oninput="updateComponentPercentage(this); calculateOverallSubjectMark('{{ student.name.replace(' ', '_') }}', '{{ subject.id }}')"
                                                            onchange="updateComponentPercentage(this); calculateOverallSubjectMark('{{ student.name.replace(' ', '_') }}', '{{ subject.id }}')"
                                                            title="Enter raw mark for {{ component.name }} (Max: {{ component.max_raw_mark or total_marks }})">
                                                    </div>
                                                </div>
                                                {% endfor %}

                                                <div class="overall-column">
                                                    <div class="overall-percentage-display">
                                                        <span id="overall_percentage_display_{{ student.name.replace(' ', '_') }}_{{ subject.id }}" class="overall-percentage-value">0%</span>
                                                        <div class="percentage-bar">
                                                            <div class="percentage-fill" style="width: 0%"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Hidden field for the overall subject mark (calculated from components) -->
                                            <input type="hidden"
                                                name="mark_{{ student.name.replace(' ', '_') }}_{{ subject.id }}"
                                                id="overall_mark_{{ student.name.replace(' ', '_') }}_{{ subject.id }}"
                                                value="0">
                                            <input type="hidden"
                                                name="percentage_{{ student.name.replace(' ', '_') }}_{{ subject.id }}"
                                                id="hidden_percentage_{{ student.name.replace(' ', '_') }}_{{ subject.id }}"
                                                value="0">
                                        </div>
                                        {% else %}
                                        <div class="simplified-mark-entry">
                                            <div class="mark-input-container">
                                                <input type="number"
                                                    name="mark_{{ student.name.replace(' ', '_') }}_{{ subject.id }}"
                                                    value="0"
                                                    min="0"
                                                    max="{{ total_marks }}"
                                                    required
                                                    class="student-mark"
                                                    data-student="{{ student.name.replace(' ', '_') }}"
                                                    data-subject="{{ subject.id }}"
                                                    data-subject-index="{{ loop.index0 }}"
                                                    data-max-mark="{{ total_marks }}"
                                                    oninput="updatePercentage(this)"
                                                    onchange="updatePercentage(this)"
                                                    title="Enter raw mark for {{ subject.name }} (Max: {{ total_marks }})">
                                            </div>
                                            <div class="percentage-display">
                                                <span id="percentage_{{ student.name.replace(' ', '_') }}_{{ subject.id }}" class="percentage-value">0%</span>
                                                <div class="percentage-bar">
                                                    <div class="percentage-fill" style="width: 0%"></div>
                                                </div>
                                                <input type="hidden"
                                                    name="percentage_{{ student.name.replace(' ', '_') }}_{{ subject.id }}"
                                                    id="hidden_percentage_{{ student.name.replace(' ', '_') }}_{{ subject.id }}"
                                                    value="0">
                                            </div>
                                        </div>
                                        {% endif %}
                                    </td>
                                    {% endfor %}
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <button type="submit" name="submit_marks" value="1" class="btn" id="submit-marks-btn">
                            Submit All Marks
                        </button>
                    </div>
                </form>
            </div>
            {% endif %}
        </div>

        <!-- Generate Marksheet Tab Content -->
        <div id="generate-marksheet-tab" class="tab-content-container {% if active_tab == 'generate-marksheet' %}active{% endif %}">
            <div class="dashboard-card">
                <div class="card-header">
                    <span>Generate Grade Marksheet (All Streams)</span>
                    <small>Select grade, term, and assessment type to generate a marksheet for all streams in the grade</small>
                </div>
                <form id="stream-marksheet-form" method="POST" action="{{ url_for('classteacher.dashboard') }}" class="login-form">
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                <div class="form-group">
                    <label for="stream_grade">Grade</label>
                    <select id="stream_grade" name="stream_grade" required onchange="checkStreamStatus()">
                        <option value="">Select Grade</option>
                        {% for grade_option in grades %}
                        <option value="{{ grade_option }}">{{ grade_option }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="form-group">
                    <label for="stream_term">Term</label>
                    <select id="stream_term" name="stream_term" required onchange="checkStreamStatus()">
                        <option value="">Select Term</option>
                        {% for term_option in terms %}
                        <option value="{{ term_option }}">{{ term_option }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="form-group">
                    <label for="stream_assessment_type">Assessment Type</label>
                    <select id="stream_assessment_type" name="stream_assessment_type" required onchange="checkStreamStatus()">
                        <option value="">Select Assessment Type</option>
                        {% for assessment_option in assessment_types %}
                        <option value="{{ assessment_option }}">{{ assessment_option }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="form-group" style="grid-column: span 2;">
                    <div class="marksheet-info">
                        <strong>Grade Marksheet Status:</strong>
                        <p>A grade marksheet combines results from all streams in the selected grade. All streams must have their marks entered before generating.</p>
                        <button type="button" class="refresh-status" onclick="checkStreamStatus()">Refresh Status</button>
                    </div>

                    <div id="stream-status-container" class="stream-status-list" style="display: none;">
                        <div id="stream-status-list">
                            <!-- Stream status items will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Parent Notification Option -->
                <div class="form-group" style="grid-column: span 2;">
                    <div style="background: rgba(102, 126, 234, 0.1); border: 1px solid rgba(102, 126, 234, 0.2); border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; gap: 10px; cursor: pointer; font-weight: 500;">
                            <input type="checkbox" name="notify_parents" style="transform: scale(1.2);">
                            <span>📧 Notify Parents via Email</span>
                        </label>
                        <p style="font-size: 12px; color: #666; margin-top: 8px; margin-left: 30px;">
                            Send automatic email notifications to all parents when the report is generated.
                            Only parents with active accounts and email notifications enabled will receive emails.
                        </p>
                    </div>
                </div>

                <div class="form-group" style="grid-column: span 2; text-align: center;">
                    <button type="submit" id="preview-marksheet-btn" name="generate_stream_marksheet" value="1" class="btn" style="background-color: #007BFF; margin-right: 10px;" disabled>
                        Preview Grade Marksheet
                    </button>
                    <button type="submit" id="download-marksheet-btn" name="download_stream_marksheet" value="1" class="btn" style="background-color: #28A745;" disabled>
                        Download Grade Marksheet
                    </button>
                </div>
            </form>
        </div>

        <script>
            function sortReports(sortBy) {
                // Get current URL parameters
                const urlParams = new URLSearchParams(window.location.search);

                // Update or add the sort parameter
                urlParams.set('sort', sortBy);

                // Redirect to the new URL with updated parameters
                window.location.href = window.location.pathname + '?' + urlParams.toString();
            }

            function filterReports() {
                // Get current URL parameters
                const urlParams = new URLSearchParams(window.location.search);

                // Get filter values
                const gradeFilter = document.getElementById('filter-grade').value;
                const termFilter = document.getElementById('filter-term').value;

                // Update parameters
                if (gradeFilter) {
                    urlParams.set('filter_grade', gradeFilter);
                } else {
                    urlParams.delete('filter_grade');
                }

                if (termFilter) {
                    urlParams.set('filter_term', termFilter);
                } else {
                    urlParams.delete('filter_term');
                }

                // Redirect to the new URL with updated parameters
                window.location.href = window.location.pathname + '?' + urlParams.toString();
            }

            function resetFilters() {
                // Get current URL parameters
                const urlParams = new URLSearchParams(window.location.search);

                // Remove filter parameters
                urlParams.delete('filter_grade');
                urlParams.delete('filter_term');

                // Keep sort parameter if it exists
                const sortParam = urlParams.get('sort');

                // Clear all parameters
                urlParams.forEach((value, key) => {
                    urlParams.delete(key);
                });

                // Add back sort parameter if it existed
                if (sortParam) {
                    urlParams.set('sort', sortParam);
                }

                // Redirect to the new URL with updated parameters
                window.location.href = window.location.pathname + '?' + urlParams.toString();
            }
        </script>

        <!-- Conditional Download and Preview Class PDF Report Section -->
        {% if show_download_button %}
        <div class="text-center mt-4">
            <a href="{{ url_for('classteacher.edit_class_marks', grade=grade, stream=stream, term=term, assessment_type=assessment_type) }}" class="btn" style="background-color: #17a2b8; margin-right: 10px;">
                Edit Class Marks
            </a>
            <a href="{{ url_for('classteacher.preview_class_report', grade=grade, stream=stream, term=term, assessment_type=assessment_type) }}" class="btn" style="background-color: #007BFF; margin-right: 10px;">
                Preview Class Report
            </a>
            <a href="{{ url_for('classteacher.preview_class_report', grade=grade, stream=stream, term=term, assessment_type=assessment_type) }}" class="btn" style="background-color: #28A745; margin-right: 10px;">
                <i class="print-icon">🖨️</i> Print/PDF
            </a>
            <button onclick="confirmDeleteReport('{{ grade }}', '{{ stream }}', '{{ term }}', '{{ assessment_type }}')" class="btn" style="background-color: #e74c3c;">
                Delete Marksheet
            </button>
            <p>Click above to edit, preview, print, download, or delete the class report.</p>
        </div>
        {% endif %}

        <!-- Conditional Individual Reports Section -->
        {% if show_individual_report_button %}
        <div class="text-center mt-4">
            <h3>Individual Learner Reports</h3>
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>Student Name</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for student in students %}
                        <tr>
                            <td>{{ student.name }}</td>
                            <td>
                                <a href="{{ url_for('classteacher.preview_individual_report', grade=grade, stream=stream, term=term, assessment_type=assessment_type, student_name=student.name) }}" class="btn" style="background-color: #007BFF;">Preview Report</a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            <button onclick="downloadAllIndividualReports('{{ grade }}', '{{ stream }}', '{{ term }}', '{{ assessment_type }}')" class="btn mt-3" id="downloadAllBtn">
                Download All Individual Reports
            </button>
            <p>Click above to download a ZIP file containing individual reports for all learners.</p>
        </div>
        {% endif %}
    </div>

    <!-- Enhanced Floating Action System -->
    <div class="floating-action-system">
        <div class="fab-menu" id="fab-menu">
            <a href="#" onclick="navigateToFeature('upload-marks'); toggleEnhancedFab(); return false;" class="fab-item">
                <i class="fas fa-upload"></i>
                <span>Upload Marks</span>
            </a>
            <a href="#" onclick="downloadTemplate(); toggleEnhancedFab(); return false;" class="fab-item">
                <i class="fas fa-download"></i>
                <span>Download Template</span>
            </a>
            <a href="{{ url_for('classteacher.collaborative_marks_dashboard') }}" class="fab-item">
                <i class="fas fa-users"></i>
                <span>Collaborative Marks</span>
            </a>
            <a href="#" onclick="navigateToFeature('generate-reports'); toggleEnhancedFab(); return false;" class="fab-item">
                <i class="fas fa-file-pdf"></i>
                <span>Generate Reports</span>
            </a>
            <a href="{{ url_for('classteacher.analytics_dashboard') }}" class="fab-item">
                <i class="fas fa-chart-line"></i>
                <span>Analytics</span>
            </a>
        </div>
        <button class="fab-main" onclick="toggleEnhancedFab()" id="fab-main-btn">
            <i class="fas fa-plus" id="fab-icon"></i>
        </button>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteReportModal" class="modal">
        <div class="modal-content">
            <h3>Confirm Deletion</h3>
            <p id="deleteReportMessage">Are you sure you want to delete this marksheet? This action cannot be undone.</p>
            <div class="modal-buttons">
                <form id="deleteReportForm" method="POST" action="">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <button type="button" onclick="closeDeleteReportModal()" class="cancel-btn">Cancel</button>
                    <button type="submit" class="confirm-delete-btn">Delete</button>
                </form>
            </div>
        </div>
    </div>

    <footer>
        <p>© 2025 {{ school_info.school_name|default('School Management System') }} - All Rights Reserved</p>
    </footer>

    <style>
        /* Deletion Confirmation Styles */
        .deletion-confirmation {
            padding: 10px;
        }
        .deletion-confirmation h3 {
            color: #155724;
            margin-top: 0;
            font-size: 1.2rem;
        }
        .deletion-confirmation p {
            margin: 8px 0;
        }
        .deletion-confirmation strong {
            font-weight: 600;
        }

        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            width: 50%;
            max-width: 500px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        .modal-buttons {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
            gap: 10px;
        }
        .cancel-btn {
            background-color: #95a5a6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        .cancel-btn:hover {
            background-color: #7f8c8d;
        }
        .confirm-delete-btn {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        .confirm-delete-btn:hover {
            background-color: #c0392b;
        }

        /* Tab styles */
        .tabs {
            width: 100%;
        }

        .tab-header {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }

        .tab-btn {
            padding: 10px 20px;
            cursor: pointer;
            border: 1px solid transparent;
            border-bottom: none;
            margin-right: 5px;
            border-radius: 5px 5px 0 0;
            background-color: #f8f9fa;
        }

        .tab-btn.active {
            background-color: #fff;
            border-color: #ddd;
            border-bottom-color: #fff;
            margin-bottom: -1px;
            font-weight: bold;
        }

        .tab-content {
            padding: 20px 0;
        }

        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block;
        }

        .help-text {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }

        /* Print Controls Dropdown Styles */
        .dropdown-menu {
            position: absolute;
            background-color: white;
            min-width: 160px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
            z-index: 100;
            border-radius: 4px;
            padding: 8px 0;
            right: 0;
            top: 100%;
            margin-top: 5px;
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            text-decoration: none;
            color: #333;
            transition: background-color 0.2s;
            white-space: nowrap;
        }

        .dropdown-item:hover {
            background-color: #f1f1f1;
        }

        .dropdown-toggle::after {
            content: "";
            display: inline-block;
            margin-left: 8px;
            vertical-align: middle;
            border-top: 4px solid;
            border-right: 4px solid transparent;
            border-left: 4px solid transparent;
        }

        .print-icon, .download-icon, .info-icon {
            margin-right: 8px;
            font-style: normal;
        }

        .close-btn:hover {
            color: #e74c3c;
        }

        /* Performance indicator styles */
        .performance-ee {
            color: #28a745;
            font-weight: bold;
        }

        .performance-me {
            color: #17a2b8;
            font-weight: bold;
        }

        .performance-ae {
            color: #ffc107;
            font-weight: bold;
        }

        .performance-be {
            color: #dc3545;
            font-weight: bold;
        }

        /* Subject marks configuration styles */
        .subject-marks-config {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }

        .subject-marks-config h4 {
            margin-top: 0;
            margin-bottom: 10px;
            color: #495057;
            font-size: 18px;
        }

        .subject-marks-config h5 {
            margin-top: 0;
            margin-bottom: 10px;
            color: #495057;
            font-size: 16px;
        }

        .subject-selection-container {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e9f7fe;
            border-radius: 5px;
            border: 1px solid #b3e5fc;
        }

        .subject-selection-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .selection-controls {
            display: flex;
            gap: 10px;
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
            border-radius: 4px;
            border: none;
            background-color: #007bff;
            color: white;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .btn-sm:hover {
            background-color: #0069d9;
        }

        .btn-sm:last-child {
            background-color: #6c757d;
        }

        .btn-sm:last-child:hover {
            background-color: #5a6268;
        }

        .subject-marks-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .subject-mark-item {
            display: flex;
            flex-direction: column;
            gap: 8px;
            padding: 10px;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            background-color: white;
            transition: all 0.2s ease;
        }

        .subject-mark-item:hover {
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .subject-mark-item.disabled {
            opacity: 0.6;
            background-color: #f1f1f1;
        }

        .subject-selection {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .subject-checkbox {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }

        .checkbox-label {
            font-weight: bold;
            font-size: 14px;
            cursor: pointer;
        }

        .marks-input-container {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* Validation Error and Confirmation Dialog Animations */
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .validation-error-message {
            animation: slideInRight 0.3s ease-out;
        }

        .confirmation-dialog {
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        .marks-label {
            font-size: 13px;
            color: #495057;
            white-space: nowrap;
        }

        .subject-mark-item input[type="number"] {
            width: 80px;
            padding: 6px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }

        .subject-mark-item input:focus {
            border-color: #80bdff;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        /* Simplified mark entry styles */
        .simplified-mark-entry {
            display: flex;
            flex-direction: column;
            width: 100%;
            align-items: center;
        }

        .mark-input-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            margin-bottom: 3px;
        }

        .mark-input-container input {
            width: 45px;
            text-align: center;
            padding: 4px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
            background-color: #fff;
            box-shadow: inset 0 1px 2px rgba(0,0,0,0.05);
        }

        .mark-input-container input:focus {
            border-color: #80bdff;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .mark-input-container input.has-value {
            background-color: #e8f5e9;
            border-color: #66bb6a;
        }

        @keyframes mark-updated {
            0% { background-color: #fff; }
            50% { background-color: #e3f2fd; }
            100% { background-color: #e8f5e9; }
        }

        .mark-updated {
            animation: mark-updated 0.5s ease;
        }

        .subject-info {
            font-size: 11px;
            color: #666;
            display: block;
            text-align: center;
        }

        .percentage-display {
            display: flex;
            flex-direction: column;
            width: 100%;
            align-items: center;
        }

        .percentage-value {
            font-size: 12px;
            margin-bottom: 2px;
            font-weight: bold;
        }

        .percentage-bar {
            height: 4px;
            background-color: #e9ecef;
            border-radius: 2px;
            overflow: hidden;
            width: 100%;
        }

        .percentage-fill {
            height: 100%;
            background-color: #6c757d;
            transition: width 0.3s ease;
        }

        /* Color the percentage fill based on performance level */
        .performance-ee .percentage-fill {
            background-color: #28a745;
        }

        .performance-me .percentage-fill {
            background-color: #17a2b8;
        }

        .performance-ae .percentage-fill {
            background-color: #ffc107;
        }

        .performance-be .percentage-fill {
            background-color: #dc3545;
        }

        /* Student row styles */
        tr {
            transition: background-color 0.2s ease;
        }

        tbody tr:hover {
            background-color: #f1f8ff;
        }

        tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        tbody tr td:first-child {
            font-weight: bold;
            color: #495057;
            padding: 8px;
            border-right: 1px solid #e9ecef;
            position: sticky;
            left: 0;
            background-color: inherit;
            z-index: 1;
        }

        /* Composite subject styles */
        .composite-subject-entry {
            display: flex;
            flex-direction: column;
            gap: 10px;
            padding: 5px;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            background-color: #f8f9fa;
        }

        /* Improved composite subject styles */
        .composite-subject-entry-improved {
            width: 100%;
            padding: 5px;
            border-radius: 5px;
            background-color: #f8f9fa;
        }

        .composite-components-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 5px;
            width: 100%;
        }

        .component-column {
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 5px;
            border: 1px solid #e9ecef;
        }

        .overall-column {
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: #e9f7fe;
            border-radius: 4px;
            padding: 5px;
            border: 1px solid #b3e5fc;
        }

        .component-percentage {
            font-size: 10px;
            text-align: center;
            margin-top: 2px;
            font-weight: bold;
        }

        .overall-percentage-display {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
        }

        .overall-percentage-value {
            font-size: 14px;
            font-weight: bold;
            color: #007bff;
            padding: 3px 0;
        }

        .component-mark-entry {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 5px;
            border-bottom: 1px dashed #dee2e6;
        }

        .component-mark-entry:last-child {
            border-bottom: none;
        }

        .component-label {
            font-size: 12px;
            font-weight: bold;
            color: #495057;
            margin-bottom: 3px;
        }

        /* Styling for composite subject headers */
        .composite-subject-header {
            background-color: #e9f7fe;
            border-bottom: 2px solid #007bff;
            position: relative;
        }

        /* Add column headers for composite subjects */
        .composite-subject-header .component-headers {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 5px;
            width: 100%;
            margin-top: 8px;
            font-size: 11px;
            font-weight: normal;
            background-color: #e3f2fd;
            border-radius: 4px;
            padding: 4px;
            border: 1px solid #b3e5fc;
        }

        .composite-subject-header .component-header-item {
            text-align: center;
            color: #0d47a1;
            font-size: 10px;
            font-weight: bold;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding: 3px 0;
        }

        .composite-subject-header .component-header-weight {
            display: block;
            font-size: 9px;
            color: #1565c0;
            font-style: italic;
        }

        .composite-subject-header .overall-header {
            background-color: #bbdefb;
            border-radius: 3px;
            font-weight: bold;
        }

        .composite-subject-header::after {
            content: "";
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #007bff;
        }

        /* Styling for component marks */
        .component-marks-container {
            margin-top: 5px;
            padding-top: 5px;
            border-top: 1px dashed #dee2e6;
        }

        /* Overall subject percentage display */
        .overall-subject-percentage {
            text-align: center;
            font-weight: bold;
            margin-bottom: 8px;
            padding: 5px;
            background-color: #f0f8ff;
            border-radius: 4px;
            border: 1px solid #b8daff;
        }

        .overall-percentage-label {
            color: #0056b3;
        }

        .overall-percentage-value {
            font-size: 14px;
            color: #007bff;
        }

        /* Styles for dynamic composite subject components */
        .composite-subject-item {
            border: 1px solid #d0e8f7;
            border-radius: 6px;
            padding: 8px;
            background-color: #f0f9ff;
            box-shadow: 0 1px 3px rgba(0, 123, 255, 0.1);
            transition: all 0.2s ease;
        }

        .composite-subject-item:hover {
            box-shadow: 0 2px 5px rgba(0, 123, 255, 0.15);
        }

        .subject-mark-item {
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 8px;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            transition: all 0.2s ease;
            margin-bottom: 8px;
        }

        .subject-mark-item:hover {
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .subject-marks-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 12px;
        }

        .subject-selection {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            padding-bottom: 6px;
            border-bottom: 1px solid #e9ecef;
        }

        .checkbox-label {
            font-weight: bold;
            font-size: 14px;
            color: #333;
        }

        .subject-checkbox {
            width: 16px;
            height: 16px;
            cursor: pointer;
            accent-color: #007bff;
        }

        .composite-marks-container {
            margin-top: 8px;
            background-color: rgba(255, 255, 255, 0.7);
            border-radius: 6px;
            padding: 8px;
            border: 1px solid #e0e0e0;
        }

        .composite-marks-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-weight: bold;
            color: #0056b3;
            border-bottom: 1px solid #b8daff;
            padding-bottom: 6px;
            flex-wrap: wrap;
        }

        .composite-marks-header span {
            font-size: 13px;
            margin-right: 5px;
        }

        .composite-marks-header small {
            color: #666;
            font-weight: normal;
            background-color: #e6f3ff;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 11px;
            white-space: nowrap;
        }

        .component-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 4px;
        }

        .component-marks-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #f8f9fa;
            padding: 6px 8px;
            border-radius: 4px;
            border: 1px solid #e9ecef;
            margin-bottom: 4px;
        }

        .component-label {
            flex: 1;
            font-size: 13px;
            color: #495057;
            font-weight: 500;
        }

        .component-marks-input {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .component-max-marks {
            width: 60px;
            padding: 6px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            text-align: center;
            font-size: 13px;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .component-max-marks:focus {
            border-color: #80bdff;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .component-weight {
            font-size: 12px;
            color: #6c757d;
            background-color: #e9ecef;
            padding: 2px 5px;
            border-radius: 3px;
            text-align: center;
            display: none; /* Hide the component weight since we're not using it */
        }

        .marks-input-container {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 8px;
            background-color: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }

        .marks-label {
            font-size: 13px;
            color: #495057;
            font-weight: 500;
        }

        .subject-total-marks {
            width: 60px;
            padding: 6px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            text-align: center;
            font-size: 13px;
        }

        .subject-total-marks:focus {
            border-color: #80bdff;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .subject-selection-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            background-color: #e9f7fe;
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #b3e5fc;
        }

        .subject-selection-header h5 {
            margin: 0;
            color: #0277bd;
            font-size: 16px;
        }

        .selection-controls {
            display: flex;
            gap: 10px;
        }

        .btn-sm {
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            border: none;
            transition: all 0.2s ease;
        }

        .btn-sm:first-child {
            background-color: #007bff;
            color: white;
        }

        .btn-sm:first-child:hover {
            background-color: #0069d9;
        }

        .btn-sm:last-child {
            background-color: #6c757d;
            color: white;
        }

        .btn-sm:last-child:hover {
            background-color: #5a6268;
        }

        .disabled {
            opacity: 0.6;
            background-color: #f8f9fa !important;
            border: 1px dashed #dee2e6 !important;
        }
    </style>

    <script>
        // Function to validate maximum marks (should not exceed 100)
        function validateMaxMarks(inputElement) {
            const value = parseFloat(inputElement.value) || 0;
            const maxAllowed = 100;

            if (value > maxAllowed) {
                // Cap the value at maximum allowed
                inputElement.value = maxAllowed;

                // Show visual feedback
                inputElement.style.borderColor = '#dc3545';
                inputElement.style.backgroundColor = '#fff5f5';

                // Show temporary warning message
                const warningMsg = document.createElement('div');
                warningMsg.textContent = `Maximum raw marks cannot exceed ${maxAllowed}. Value capped at ${maxAllowed}.`;
                warningMsg.style.cssText = `
                    position: absolute;
                    background: #dc3545;
                    color: white;
                    padding: 8px 12px;
                    border-radius: 4px;
                    font-size: 12px;
                    z-index: 1000;
                    margin-top: 5px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                `;

                // Position the warning near the input
                const rect = inputElement.getBoundingClientRect();
                warningMsg.style.left = rect.left + 'px';
                warningMsg.style.top = (rect.bottom + window.scrollY) + 'px';

                document.body.appendChild(warningMsg);

                // Remove warning after 3 seconds
                setTimeout(() => {
                    if (warningMsg.parentNode) {
                        warningMsg.parentNode.removeChild(warningMsg);
                    }
                    // Reset input styling
                    inputElement.style.borderColor = '';
                    inputElement.style.backgroundColor = '';
                }, 3000);

                console.log(`Max marks validation: ${value} exceeded ${maxAllowed}, capped to ${maxAllowed}`);
            }

            // Ensure minimum value is 1
            if (value < 1 && value !== 0) {
                inputElement.value = 1;
            }
        }

        // Function to update percentage based on raw mark and total marks
        function updatePercentage(inputElement) {
            console.log("Updating percentage...");

            // Get the student and subject information from data attributes
            const student = inputElement.dataset.student;
            const subject = inputElement.dataset.subject;
            const subjectIndex = inputElement.dataset.subjectIndex;

            // Get the max raw mark from the corresponding total marks input
            const totalMarksInput = document.getElementById(`total_marks_${subjectIndex}`);
            const maxRawMark = totalMarksInput ? parseFloat(totalMarksInput.value) : (parseFloat(inputElement.dataset.maxMark) || 100);

            // Get the raw mark value
            let rawMark = parseFloat(inputElement.value) || 0;

            // Ensure the raw mark doesn't exceed the max mark
            if (rawMark > maxRawMark) {
                rawMark = maxRawMark;
                inputElement.value = maxRawMark;
                console.log(`Raw mark exceeded maximum (${maxRawMark}), capped at maximum`);
            }

            // Add visual feedback for mark entry
            if (rawMark > 0) {
                inputElement.classList.add('has-value');
                inputElement.classList.add('mark-updated');
                setTimeout(() => {
                    inputElement.classList.remove('mark-updated');
                }, 500);
            } else {
                inputElement.classList.remove('has-value');
            }

            // Calculate percentage
            let percentage = 0;
            if (maxRawMark > 0) {
                percentage = (rawMark / maxRawMark) * 100;
            }
            console.log(`Initial percentage: ${percentage}`);

            // Ensure percentage doesn't exceed 100% and round to whole number
            let roundedPercentage;
            if (percentage > 100) {
                // Cap the raw mark to the maximum allowed value
                inputElement.value = maxRawMark;
                roundedPercentage = 100;
                console.log(`Percentage exceeded 100%, capped to 100%`);
            } else {
                roundedPercentage = Math.round(percentage); // Round to whole number
            }
            console.log(`Final rounded percentage: ${roundedPercentage}%`);

            // Update the percentage display
            const percentageSpan = document.getElementById(`percentage_${student}_${subject}`);
            if (percentageSpan) {
                percentageSpan.textContent = `${roundedPercentage}%`;
                console.log(`Updated percentage display to: ${roundedPercentage}%`);

                // Add visual indicator of performance level
                updatePerformanceIndicator(percentageSpan, roundedPercentage);

                // Update the percentage bar
                const percentageBar = percentageSpan.closest('.percentage-display')?.querySelector('.percentage-fill');
                if (percentageBar) {
                    percentageBar.style.width = `${Math.min(roundedPercentage, 100)}%`; // Cap at 100%
                    console.log(`Updated percentage bar width to: ${Math.min(roundedPercentage, 100)}%`);

                    // Update the color of the percentage bar based on performance level
                    if (roundedPercentage >= 75) {
                        percentageBar.style.backgroundColor = '#28a745'; // Exceeding Expectation
                    } else if (roundedPercentage >= 50) {
                        percentageBar.style.backgroundColor = '#17a2b8'; // Meeting Expectation
                    } else if (roundedPercentage >= 30) {
                        percentageBar.style.backgroundColor = '#ffc107'; // Approaching Expectation
                    } else {
                        percentageBar.style.backgroundColor = '#dc3545'; // Below Expectation
                    }
                }
            } else {
                console.log(`Could not find percentage span with ID: percentage_${student}_${subject}`);
            }

            // Update the hidden percentage input
            const hiddenPercentageInput = document.getElementById(`hidden_percentage_${student}_${subject}`);
            if (hiddenPercentageInput) {
                hiddenPercentageInput.value = roundedPercentage;
                console.log(`Updated hidden percentage input to: ${roundedPercentage}`);
            }

            // Validate that the raw mark doesn't exceed the maximum
            if (rawMark > maxRawMark) {
                inputElement.value = maxRawMark;
                console.log(`Raw mark exceeded maximum, capped to: ${maxRawMark}`);
                // No need to recalculate as we've already set the correct values
            }
        }

        // Function to update component percentage and calculate overall subject mark
        function updateComponentPercentage(inputElement) {
            console.log("Updating component percentage...");

            // Get the student, subject, and component information from data attributes
            const student = inputElement.dataset.student;
            const subject = inputElement.dataset.subject;
            const component = inputElement.dataset.component;
            const componentWeight = parseFloat(inputElement.dataset.componentWeight) || 1.0;
            const subjectIndex = inputElement.dataset.subjectIndex;

            // Get the raw mark and max mark
            let rawMark = parseInt(inputElement.value) || 0;
            const maxMark = parseInt(inputElement.dataset.maxMark) || 100;

            // Ensure the raw mark doesn't exceed the max mark
            if (rawMark > maxMark) {
                rawMark = maxMark;
                inputElement.value = maxMark;
                console.log(`Raw mark exceeded maximum (${maxMark}), capped at maximum`);
            }

            // Add visual feedback for mark entry
            if (rawMark > 0) {
                inputElement.classList.add('has-value');
                inputElement.classList.add('mark-updated');
                setTimeout(() => {
                    inputElement.classList.remove('mark-updated');
                }, 500);
            } else {
                inputElement.classList.remove('has-value');
            }

            // Calculate the overall subject mark based on all components
            calculateOverallSubjectMark(student, subject);
        }

        // Function to calculate the overall subject mark from component marks
        function calculateOverallSubjectMark(student, subject) {
            console.log(`Calculating overall mark for student: ${student}, subject: ${subject}`);

            // Find all component inputs for this student and subject
            const componentInputs = document.querySelectorAll(`input.component-mark[data-student="${student}"][data-subject="${subject}"]`);
            console.log(`Found ${componentInputs.length} component inputs`);

            if (componentInputs.length === 0) return;

            let totalWeightedPercentage = 0;
            let totalWeight = 0;

            // Calculate weighted percentage for each component
            componentInputs.forEach(input => {
                const rawMark = parseInt(input.value) || 0;
                const maxMark = parseInt(input.dataset.maxMark) || 100;
                const weight = parseFloat(input.dataset.componentWeight) || 1.0;

                // Calculate percentage for this component (out of 100%)
                const percentage = maxMark > 0 ? (rawMark / maxMark) * 100 : 0;

                // Apply the weight to get the weighted contribution to the final mark
                const weightedContribution = percentage * weight;

                // Add to the total
                totalWeightedPercentage += weightedContribution;
                totalWeight += weight;

                console.log(`Component: ${input.dataset.component}, Raw: ${rawMark}, Max: ${maxMark}, Weight: ${weight}, Percentage: ${percentage.toFixed(1)}%, Weighted: ${weightedContribution.toFixed(1)}`);
            });

            // Calculate overall percentage using weighted average
            const overallPercentage = totalWeight > 0 ? totalWeightedPercentage / totalWeight : 0;
            console.log(`Total weighted percentage: ${totalWeightedPercentage.toFixed(1)}, Total weight: ${totalWeight}, Overall: ${overallPercentage.toFixed(1)}%`);

            // Round the overall percentage to a whole number
            const roundedOverallPercentage = Math.round(overallPercentage);

            // Update hidden fields for overall mark and percentage
            const overallMarkField = document.getElementById(`overall_mark_${student}_${subject}`);
            const overallPercentageField = document.getElementById(`hidden_percentage_${student}_${subject}`);

            if (overallMarkField && overallPercentageField) {
                overallMarkField.value = roundedOverallPercentage;
                overallPercentageField.value = roundedOverallPercentage;
                console.log(`Updated hidden fields: mark=${roundedOverallPercentage}, percentage=${roundedOverallPercentage}`);
            }

            // Update the overall percentage display
            const overallPercentageDisplay = document.getElementById(`overall_percentage_display_${student}_${subject}`);
            if (overallPercentageDisplay) {
                overallPercentageDisplay.textContent = `${roundedOverallPercentage}%`;
                console.log(`Updated display to: ${roundedOverallPercentage}%`);

                // Update the color based on performance level
                if (roundedOverallPercentage >= 75) {
                    overallPercentageDisplay.style.color = '#28a745'; // Exceeding Expectation
                    overallPercentageDisplay.style.fontWeight = 'bold';
                } else if (roundedOverallPercentage >= 50) {
                    overallPercentageDisplay.style.color = '#17a2b8'; // Meeting Expectation
                    overallPercentageDisplay.style.fontWeight = 'bold';
                } else if (roundedOverallPercentage >= 30) {
                    overallPercentageDisplay.style.color = '#ffc107'; // Approaching Expectation
                    overallPercentageDisplay.style.fontWeight = 'bold';
                } else {
                    overallPercentageDisplay.style.color = '#dc3545'; // Below Expectation
                    overallPercentageDisplay.style.fontWeight = 'bold';
                }

                // Update the percentage bar
                const percentageFill = overallPercentageDisplay.nextElementSibling?.querySelector('.percentage-fill');
                if (percentageFill) {
                    percentageFill.style.width = `${Math.min(roundedOverallPercentage, 100)}%`;

                    // Set color based on performance level
                    if (roundedOverallPercentage >= 75) {
                        percentageFill.style.backgroundColor = '#28a745'; // Exceeding Expectation
                    } else if (roundedOverallPercentage >= 50) {
                        percentageFill.style.backgroundColor = '#17a2b8'; // Meeting Expectation
                    } else if (roundedOverallPercentage >= 30) {
                        percentageFill.style.backgroundColor = '#ffc107'; // Approaching Expectation
                    } else {
                        percentageFill.style.backgroundColor = '#dc3545'; // Below Expectation
                    }
                }
            }
        }

        // Function to update the visual performance indicator
        function updatePerformanceIndicator(element, percentage) {
            // Remove existing performance classes
            element.classList.remove('performance-ee', 'performance-me', 'performance-ae', 'performance-be');

            // Add appropriate class based on percentage
            if (percentage >= 75) {
                element.classList.add('performance-ee'); // Exceeding Expectation
            } else if (percentage >= 50) {
                element.classList.add('performance-me'); // Meeting Expectation
            } else if (percentage >= 30) {
                element.classList.add('performance-ae'); // Approaching Expectation
            } else {
                element.classList.add('performance-be'); // Below Expectation
            }
        }

        // Function to update component max marks for composite subjects
        function updateComponentMaxMarks(subject, subjectIndex) {
            console.log(`Updating component max marks for subject: ${subject}, index: ${subjectIndex}`);

            // Get all component max marks inputs for this subject
            const componentInputs = document.querySelectorAll(`.component-max-marks[data-subject="${subject}"]`);
            if (!componentInputs.length) {
                console.log(`No component inputs found for subject ${subject}`);
                return;
            }

            // Calculate the total marks from all components
            let totalMarks = 0;
            componentInputs.forEach(input => {
                const componentMarks = parseInt(input.value) || 0;
                totalMarks += componentMarks;
            });

            console.log(`Calculated total marks from components: ${totalMarks}`);

            // Update the total marks display
            const totalCompositeSpan = document.getElementById(`total_composite_${subjectIndex}`);
            if (totalCompositeSpan) {
                totalCompositeSpan.textContent = totalMarks;
                console.log(`Updated total composite display to: ${totalMarks}`);
            }

            // Update the hidden total marks input
            const totalMarksInput = document.getElementById(`total_marks_${subjectIndex}`);
            if (totalMarksInput) {
                totalMarksInput.value = totalMarks;
                console.log(`Updated hidden total marks input to: ${totalMarks}`);
            }

            // Update the subject info display in the table header
            const subjectInfoSpan = document.getElementById(`subject_info_${subjectIndex}`);
            if (subjectInfoSpan) {
                subjectInfoSpan.textContent = `Max Raw: ${totalMarks}`;
                console.log(`Updated subject info display to: Max Raw: ${totalMarks}`);
            }

            // Update all component mark inputs with their new max values
            componentInputs.forEach(input => {
                const componentId = input.dataset.component;
                const componentMaxMarks = parseInt(input.value) || 0;

                // Find all student mark inputs for this component
                const studentComponentInputs = document.querySelectorAll(`.component-mark[data-subject="${subject}"][data-component="${componentId}"]`);

                // Update each student's component mark input
                studentComponentInputs.forEach(studentInput => {
                    studentInput.max = componentMaxMarks;
                    studentInput.dataset.maxMark = componentMaxMarks;

                    // If the current value exceeds the new max, cap it
                    const currentValue = parseInt(studentInput.value) || 0;
                    if (currentValue > componentMaxMarks) {
                        studentInput.value = componentMaxMarks;
                    }

                    // Update the component percentage
                    updateComponentPercentage(studentInput);
                });

                console.log(`Updated ${studentComponentInputs.length} student inputs for component ${componentId}`);
            });

            // Update all students' marks for this subject to recalculate with the new max marks
            updateAllStudentMarksForSubject(subject);

            console.log(`Finished updating component max marks for subject: ${subject}`);
        }

        // Function to update all student marks for a subject when max marks change
        function updateAllStudentMarksForSubject(subject) {
            // Find all component inputs for this subject across all students
            const componentInputs = document.querySelectorAll(`input.component-mark[data-subject="${subject}"]`);

            // Group by student
            const studentMap = new Map();
            componentInputs.forEach(input => {
                const student = input.dataset.student;
                if (!studentMap.has(student)) {
                    studentMap.set(student, []);
                }
                studentMap.get(student).push(input);
            });

            // Update each student's marks
            studentMap.forEach((inputs, student) => {
                calculateOverallSubjectMark(student, subject);
            });
        }

        // Function to add selected subjects to the form before submission
        function addSelectedSubjectsToForm() {
            const form = document.getElementById('marks-form');
            if (!form) {
                console.error('Form not found!');
                return;
            }

            const selectedSubjects = [];

            // Get all checked subject checkboxes
            const checkboxes = document.querySelectorAll('.subject-checkbox:checked');
            checkboxes.forEach(checkbox => {
                selectedSubjects.push(checkbox.value);
            });

            // Remove any existing selected_subjects inputs
            const existingInputs = form.querySelectorAll('input[name="selected_subjects"]');
            existingInputs.forEach(input => input.remove());

            // Add new selected_subjects inputs
            selectedSubjects.forEach(subjectId => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'selected_subjects';
                input.value = subjectId;
                form.appendChild(input);
            });

            console.log('Added selected subjects to form:', selectedSubjects);
            return selectedSubjects.length > 0;
        }

        // Function to initialize selected subjects when page loads
        function initializeSelectedSubjects() {
            // Add selected subjects to form on page load
            addSelectedSubjectsToForm();

            // Add event listeners to checkboxes to update selected subjects when changed
            const checkboxes = document.querySelectorAll('.subject-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    addSelectedSubjectsToForm();
                });
            });
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeSelectedSubjects();
        });

        // Function to validate form before submission
        function validateAndSubmitForm() {
            console.log('=== FORM VALIDATION STARTED ===');

            // Ensure selected subjects are added to form
            const hasSelectedSubjects = addSelectedSubjectsToForm();

            if (!hasSelectedSubjects) {
                console.log('No subjects selected - showing error message');
                showValidationError('Please select at least one subject before submitting marks.');
                return false; // Prevent form submission
            }

            // Check if any marks have been entered
            const markInputs = document.querySelectorAll('.student-mark, .component-mark');
            let hasMarks = false;

            markInputs.forEach(input => {
                if (input.value && parseInt(input.value) > 0) {
                    hasMarks = true;
                }
            });

            if (!hasMarks) {
                console.log('No marks entered - showing confirmation dialog');
                showConfirmationDialog(
                    'No marks have been entered. Are you sure you want to submit an empty marksheet?',
                    function() {
                        console.log('User confirmed empty submission');
                        submitMarksForm();
                    }
                );
                return false; // Prevent form submission for now
            }

            console.log('Form validation passed, submitting...');
            return true; // Allow form submission
        }

        // Function to show validation error
        function showValidationError(message) {
            // Remove any existing error messages
            const existingError = document.querySelector('.validation-error-message');
            if (existingError) {
                existingError.remove();
            }

            // Create error message element
            const errorDiv = document.createElement('div');
            errorDiv.className = 'validation-error-message';
            errorDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #dc3545;
                color: white;
                padding: 15px 20px;
                border-radius: 5px;
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                z-index: 10000;
                max-width: 400px;
                font-size: 14px;
                animation: slideInRight 0.3s ease-out;
            `;
            errorDiv.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" style="
                        background: none;
                        border: none;
                        color: white;
                        font-size: 16px;
                        cursor: pointer;
                        margin-left: auto;
                    ">&times;</button>
                </div>
            `;

            document.body.appendChild(errorDiv);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (errorDiv.parentElement) {
                    errorDiv.remove();
                }
            }, 5000);
        }

        // Function to show confirmation dialog
        function showConfirmationDialog(message, onConfirm) {
            // Remove any existing confirmation dialog
            const existingDialog = document.querySelector('.confirmation-dialog');
            if (existingDialog) {
                existingDialog.remove();
            }

            // Create confirmation dialog
            const dialogDiv = document.createElement('div');
            dialogDiv.className = 'confirmation-dialog';
            dialogDiv.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10001;
            `;

            dialogDiv.innerHTML = `
                <div style="
                    background: white;
                    padding: 30px;
                    border-radius: 10px;
                    max-width: 500px;
                    width: 90%;
                    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
                ">
                    <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 20px;">
                        <i class="fas fa-question-circle" style="color: #ffc107; font-size: 24px;"></i>
                        <h3 style="margin: 0; color: #333;">Confirm Submission</h3>
                    </div>
                    <p style="margin-bottom: 25px; color: #666; line-height: 1.5;">${message}</p>
                    <div style="display: flex; gap: 10px; justify-content: flex-end;">
                        <button onclick="closeConfirmationDialog()" style="
                            padding: 10px 20px;
                            border: 1px solid #ddd;
                            background: white;
                            color: #666;
                            border-radius: 5px;
                            cursor: pointer;
                            font-size: 14px;
                        ">Cancel</button>
                        <button onclick="confirmSubmission()" style="
                            padding: 10px 20px;
                            border: none;
                            background: #007bff;
                            color: white;
                            border-radius: 5px;
                            cursor: pointer;
                            font-size: 14px;
                        ">Yes, Submit</button>
                    </div>
                </div>
            `;

            document.body.appendChild(dialogDiv);

            // Store the callback function
            window.confirmationCallback = onConfirm;
        }

        // Function to close confirmation dialog
        function closeConfirmationDialog() {
            const dialog = document.querySelector('.confirmation-dialog');
            if (dialog) {
                dialog.remove();
            }
            window.confirmationCallback = null;
        }

        // Function to confirm submission
        function confirmSubmission() {
            closeConfirmationDialog();
            if (window.confirmationCallback) {
                window.confirmationCallback();
            }
        }

        // Function to submit marks form programmatically
        function submitMarksForm() {
            console.log('Submitting marks form programmatically...');
            const form = document.getElementById('marks-form');
            if (form) {
                // Create a hidden submit button to trigger the form submission
                const submitBtn = document.createElement('input');
                submitBtn.type = 'hidden';
                submitBtn.name = 'submit_marks';
                submitBtn.value = '1';
                form.appendChild(submitBtn);

                // Submit the form
                form.submit();
            } else {
                console.error('Marks form not found!');
            }
        }

        // Enhanced Navigation System Functions
        function navigateToFeature(feature) {
            console.log(`=== NAVIGATING TO FEATURE: ${feature} ===`);

            try {
                // Update active navigation state
                updateActiveNavigation(feature);

                switch(feature) {
                    case 'upload-marks':
                        console.log('Switching to upload-marks tab');
                        switchMainTab('upload-marks');
                        // Auto-scroll to the upload section after tab switch
                        setTimeout(() => {
                            const uploadSection = document.getElementById('upload-marks-tab');
                            if (uploadSection) {
                                uploadSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                                console.log('✅ Successfully navigated to upload marks section');
                            } else {
                                console.error('❌ Upload marks section not found');
                            }
                        }, 300);
                        break;
                    case 'recent-reports':
                        console.log('Switching to recent-reports tab');
                        switchMainTab('recent-reports');
                        setTimeout(() => {
                            const reportsSection = document.getElementById('recent-reports-tab');
                            if (reportsSection) {
                                reportsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                                console.log('✅ Successfully navigated to recent reports section');
                            } else {
                                console.error('❌ Recent reports section not found');
                            }
                        }, 300);
                        break;
                    case 'generate-reports':
                        console.log('Switching to generate-marksheet tab');
                        switchMainTab('generate-marksheet');
                        // Auto-scroll to the generate section after tab switch
                        setTimeout(() => {
                            const generateSection = document.getElementById('generate-marksheet-tab');
                            if (generateSection) {
                                generateSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                                console.log('✅ Successfully navigated to generate reports section');
                            } else {
                                console.error('❌ Generate reports section not found');
                            }
                        }, 300);
                        break;
                    case 'management':
                        console.log('Scrolling to management section');
                        scrollToSection('management-section');
                        break;
                    default:
                        console.error(`❌ Unknown feature: ${feature}`);
                }

                console.log(`✅ Navigation to ${feature} completed`);

            } catch (error) {
                console.error(`❌ Error navigating to ${feature}:`, error);
            }

            return false; // Prevent default link behavior
        }

        function updateActiveNavigation(activeFeature) {
            // Remove active class from all nav items
            document.querySelectorAll('.nav-item[data-feature]').forEach(item => {
                item.classList.remove('active');
            });

            // Add active class to current feature
            const activeItem = document.querySelector(`.nav-item[data-feature="${activeFeature}"]`);
            if (activeItem) {
                activeItem.classList.add('active');
            }
        }

        // Enhanced Floating Action Button Functions
        function toggleEnhancedFab() {
            const fabMenu = document.getElementById('fab-menu');
            const fabIcon = document.getElementById('fab-icon');
            const fabMain = document.getElementById('fab-main-btn');

            if (fabMenu.classList.contains('active')) {
                fabMenu.classList.remove('active');
                fabMain.classList.remove('active');
                fabIcon.className = 'fas fa-plus';
            } else {
                fabMenu.classList.add('active');
                fabMain.classList.add('active');
                fabIcon.className = 'fas fa-times';
            }
        }

        function initializeEnhancedNavigation() {
            // Close FAB menu when clicking outside
            document.addEventListener('click', function(event) {
                const fabSystem = document.querySelector('.floating-action-system');
                if (!fabSystem.contains(event.target)) {
                    const menu = document.getElementById('fab-menu');
                    const icon = document.getElementById('fab-icon');
                    const main = document.getElementById('fab-main-btn');
                    if (menu.classList.contains('active')) {
                        menu.classList.remove('active');
                        main.classList.remove('active');
                        icon.className = 'fas fa-plus';
                    }
                }
            });

            // Initialize active navigation based on current tab
            const activeTab = sessionStorage.getItem('activeTab') || 'recent-reports';
            updateActiveNavigation(activeTab);
        }

        // Smooth scrolling function
        function scrollToSection(sectionId) {
            const section = document.getElementById(sectionId);
            if (section) {
                section.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }

        // Function to select all subjects
        function selectAllSubjects() {
            const checkboxes = document.querySelectorAll('.subject-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
                toggleSubjectVisibility(checkbox, checkbox.dataset.subjectId);
            });
        }

        // Function to deselect all subjects
        function deselectAllSubjects() {
            const checkboxes = document.querySelectorAll('.subject-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
                toggleSubjectVisibility(checkbox, checkbox.dataset.subjectId);
            });
        }

        // REMOVED DUPLICATE FUNCTION - Using the corrected version below

        // Function to update all marks for a subject when the total marks change
        function updateAllMarksForSubject(subject, subjectIndex) {
            console.log(`Updating all marks for subject: ${subject}, index: ${subjectIndex}`);

            // Get the new total marks value
            const totalMarksInput = document.getElementById(`total_marks_${subjectIndex}`);
            let totalMarks = parseFloat(totalMarksInput.value) || 100;

            // Enforce reasonable limits on total marks (maximum 100)
            if (totalMarks < 1) {
                console.log(`Total marks too low (${totalMarks}), setting to 1`);
                totalMarksInput.value = 1;
                totalMarks = 1;
            } else if (totalMarks > 100) {
                console.log(`Total marks too high (${totalMarks}), capping at 100`);
                totalMarksInput.value = 100;
                totalMarks = 100;
            }

            console.log(`Using total marks: ${totalMarks}`);

            // Update the subject info display
            const subjectInfoSpan = document.getElementById(`subject_info_${subjectIndex}`);
            if (subjectInfoSpan) {
                subjectInfoSpan.textContent = `Max Raw: ${totalMarks}`;
                console.log(`Updated subject info display to: Max Raw: ${totalMarks}`);
            }

            // Get all mark inputs for this subject
            const markInputs = document.querySelectorAll(`.student-mark[data-subject="${subject}"]`);
            console.log(`Found ${markInputs.length} mark inputs for subject ${subject}`);

            // Update each mark's max attribute and data-max-mark attribute
            markInputs.forEach(input => {
                input.max = totalMarks;
                input.dataset.maxMark = totalMarks;
                console.log(`Updated max attribute to ${totalMarks} for input: ${input.name}`);

                // If the current value exceeds the new max, cap it
                const currentValue = parseFloat(input.value) || 0;
                if (currentValue > totalMarks) {
                    console.log(`Current value ${currentValue} exceeds new max ${totalMarks}, capping it`);
                    input.value = totalMarks;
                }

                // Update the percentage display
                updatePercentage(input);
            });

            console.log(`Finished updating all marks for subject: ${subject}`);
        }

        // Function to confirm deletion of a marksheet
        function confirmDeleteReport(grade, stream, term, assessmentType) {
            // Set up the delete form action
            const deleteForm = document.getElementById('deleteReportForm');
            deleteForm.action = `/classteacher/delete_report/${grade}/${stream}/${term}/${assessmentType}`;

            // Update the confirmation message
            const deleteMessage = document.getElementById('deleteReportMessage');
            deleteMessage.textContent = `Are you sure you want to delete all marks for ${grade} ${stream} in ${term} ${assessmentType}? This action cannot be undone.`;

            // Show the modal
            const modal = document.getElementById('deleteReportModal');
            modal.style.display = 'block';
        }

        // Function to close the delete confirmation modal
        function closeDeleteReportModal() {
            // Hide the modal
            const modal = document.getElementById('deleteReportModal');
            modal.style.display = 'none';
        }

        // Close the modal if the user clicks outside of it
        window.onclick = function(event) {
            const modal = document.getElementById('deleteReportModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }

        // Function to toggle print options dropdown
        function togglePrintOptions(reportId) {
            const printOptions = document.getElementById(`print-options-${reportId}`);
            if (printOptions.style.display === 'none') {
                // Close any other open dropdowns first
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    if (menu.id !== `print-options-${reportId}`) {
                        menu.style.display = 'none';
                    }
                });
                printOptions.style.display = 'block';
            } else {
                printOptions.style.display = 'none';
            }
        }

        // Function to print a report
        function printReport(grade, stream, term, assessmentType) {
            // Open the report in a new window and print it
            const printWindow = window.open(
                `/classteacher/preview_class_report/${grade}/${stream}/${term}/${assessmentType}`,
                '_blank'
            );

            // Wait for the page to load before printing
            printWindow.addEventListener('load', function() {
                printWindow.print();
            });
        }

        // Function to print a report as PDF
        function printReportAsPDF(grade, stream, term, assessmentType) {
            // Open the report in a new window
            const pdfWindow = window.open(
                `/classteacher/preview_class_report/${grade}/${stream}/${term}/${assessmentType}`,
                '_blank'
            );

            // Wait for the page to load before showing PDF instructions
            pdfWindow.addEventListener('load', function() {
                // Add a banner with instructions at the top of the page
                const instructionsDiv = pdfWindow.document.createElement('div');
                instructionsDiv.style.position = 'fixed';
                instructionsDiv.style.top = '0';
                instructionsDiv.style.left = '0';
                instructionsDiv.style.width = '100%';
                instructionsDiv.style.padding = '15px';
                instructionsDiv.style.backgroundColor = '#f8d7da';
                instructionsDiv.style.color = '#721c24';
                instructionsDiv.style.borderBottom = '1px solid #f5c6cb';
                instructionsDiv.style.zIndex = '9999';
                instructionsDiv.style.textAlign = 'center';
                instructionsDiv.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';

                instructionsDiv.innerHTML = `
                    <h3 style="margin: 0 0 10px 0;">Save as PDF Instructions</h3>
                    <p style="margin: 0;">
                        1. Press <strong>Ctrl+P</strong> (or <strong>Cmd+P</strong> on Mac) to open the print dialog<br>
                        2. Select <strong>"Save as PDF"</strong> from the destination/printer options<br>
                        3. Click <strong>"Save"</strong> to download the PDF file
                    </p>
                    <button id="closeInstructions" style="margin-top: 10px; padding: 5px 10px; background-color: #721c24; color: white; border: none; border-radius: 3px; cursor: pointer;">
                        Close Instructions
                    </button>
                `;

                pdfWindow.document.body.insertBefore(instructionsDiv, pdfWindow.document.body.firstChild);

                // Add event listener to close button
                pdfWindow.document.getElementById('closeInstructions').addEventListener('click', function() {
                    instructionsDiv.style.display = 'none';
                });
            });
        }

        // Function to show printing instructions
        function showPrintInstructions() {
            // Create a modal for printing instructions
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';

            const modalContent = document.createElement('div');
            modalContent.className = 'modal-content';
            modalContent.style.width = '60%';

            const closeBtn = document.createElement('span');
            closeBtn.innerHTML = '&times;';
            closeBtn.className = 'close-btn';
            closeBtn.style.position = 'absolute';
            closeBtn.style.right = '20px';
            closeBtn.style.top = '10px';
            closeBtn.style.fontSize = '24px';
            closeBtn.style.cursor = 'pointer';
            closeBtn.onclick = function() {
                document.body.removeChild(modal);
            };

            const title = document.createElement('h3');
            title.textContent = 'Printing Instructions';

            const instructions = document.createElement('div');
            instructions.innerHTML = `
                <h4>How to Print/Save Reports:</h4>
                <ol>
                    <li><strong>Print directly:</strong> Click the "Print Report" button to open the report in a new tab and automatically open the print dialog.</li>
                    <li><strong>Save as PDF:</strong> Click "Save as PDF" to open the report in a new tab with instructions for saving it as a PDF file.</li>
                    <li><strong>For best results:</strong>
                        <ul>
                            <li>Use landscape orientation</li>
                            <li>Enable background colors and images</li>
                            <li>Set margins to "Narrow" or "None"</li>
                            <li>Scale to fit page if needed</li>
                        </ul>
                    </li>
                    <li><strong>Sharing with parents:</strong> The PDF format is recommended for sharing via email or messaging apps.</li>
                </ol>
            `;

            modalContent.appendChild(closeBtn);
            modalContent.appendChild(title);
            modalContent.appendChild(instructions);
            modal.appendChild(modalContent);
            document.body.appendChild(modal);

            // Close modal when clicking outside
            window.onclick = function(event) {
                if (event.target == modal) {
                    document.body.removeChild(modal);
                }
            };
        }

        // Function to update subject options based on education level
        function updateSubjects() {
            const educationLevel = document.getElementById('education_level').value;
            const formGroup = document.getElementById('education_level').closest('.form-group');
            const gradeSelect = document.getElementById('grade');

            if (educationLevel) {
                formGroup.classList.add('success');
                formGroup.classList.remove('error');

                // Store the selected education level in a data attribute for later use
                gradeSelect.dataset.educationLevel = educationLevel;

                // Fetch subjects for this education level
                fetchSubjectsForEducationLevel(educationLevel);

                // Filter grades based on education level
                filterGradesByEducationLevel(educationLevel, gradeSelect);

                console.log("Updated subjects and filtered grades for education level:", educationLevel);
            } else {
                formGroup.classList.remove('success');

                // If no education level is selected, restore all grade options
                if (window.originalGradeOptions && window.originalGradeOptions['grade']) {
                    restoreOriginalGradeOptions(gradeSelect);
                }
            }
        }

        // Function to restore original grade options
        function restoreOriginalGradeOptions(gradeSelect) {
            if (!window.originalGradeOptions || !window.originalGradeOptions[gradeSelect.id]) {
                console.error("Cannot restore original options - none stored for", gradeSelect.id);
                return;
            }

            // Clear current options
            gradeSelect.innerHTML = '';

            // Add back all original options
            window.originalGradeOptions[gradeSelect.id].forEach(opt => {
                const newOption = document.createElement('option');
                newOption.value = opt.value;
                newOption.textContent = opt.textContent;
                gradeSelect.appendChild(newOption);
            });

            console.log(`Restored ${window.originalGradeOptions[gradeSelect.id].length} original options for ${gradeSelect.id}`);
        }

        // Store all grade options globally when the page loads
        window.originalGradeOptions = {};

        // Function to filter grades based on education level
        function filterGradesByEducationLevel(educationLevel, gradeSelect) {
            console.log("Filtering grades for education level:", educationLevel);

            // Define the mapping between education levels and grades
            const educationLevelGradeMapping = {
                'lower_primary': ['Grade 1', 'Grade 2', 'Grade 3'],
                'upper_primary': ['Grade 4', 'Grade 5', 'Grade 6'],
                'junior_secondary': ['Grade 7', 'Grade 8', 'Grade 9']
            };

            // Get the grades for the selected education level
            const gradesForLevel = educationLevelGradeMapping[educationLevel] || [];
            console.log("Grades for level:", gradesForLevel);

            // Make sure we have the original options stored
            if (!window.originalGradeOptions[gradeSelect.id]) {
                // This should never happen if initialization is correct, but just in case
                console.log("Original options not found, storing now");
                storeOriginalGradeOptions();
            }

            // Get the original options for this select
            const originalOptions = window.originalGradeOptions[gradeSelect.id];
            if (!originalOptions || originalOptions.length === 0) {
                console.error("No original options found for", gradeSelect.id);
                return;
            }

            console.log(`Retrieved ${originalOptions.length} original options for ${gradeSelect.id}`);
            console.log("Original options:", originalOptions.map(opt => opt.value));

            // Clear all options from the select
            gradeSelect.innerHTML = '';

            // First add the empty option
            const emptyOption = document.createElement('option');
            emptyOption.value = '';
            emptyOption.textContent = 'Select Grade';
            gradeSelect.appendChild(emptyOption);

            // Add only the grades for the selected education level
            let optionsAdded = 0;

            // Add filtered options
            originalOptions.forEach(originalOpt => {
                // Skip the empty option as we've already added it
                if (originalOpt.value === '') return;

                // Check if this grade should be included for the selected education level
                const shouldInclude = gradesForLevel.some(grade =>
                    originalOpt.value.trim().toLowerCase() === grade.trim().toLowerCase()
                );

                if (shouldInclude) {
                    const newOption = document.createElement('option');
                    newOption.value = originalOpt.value;
                    newOption.textContent = originalOpt.textContent;
                    gradeSelect.appendChild(newOption);
                    optionsAdded++;
                }
            });

            console.log(`Added ${optionsAdded} filtered options to ${gradeSelect.id}`);

            // Reset the grade selection
            gradeSelect.selectedIndex = 0;

            // Clear the stream dropdown
            let streamSelectId = 'stream';
            if (gradeSelect.id === 'bulk_grade') {
                streamSelectId = 'bulk_stream';
            }
            const streamSelect = document.getElementById(streamSelectId);
            if (streamSelect) {
                streamSelect.innerHTML = '<option value="">Select Stream</option>';
            }
        }

        // Function to store original grade options for all grade selects
        function storeOriginalGradeOptions() {
            console.log("Storing original grade options");

            // Store options for the main form
            const gradeSelect = document.getElementById('grade');
            if (gradeSelect) {
                window.originalGradeOptions['grade'] = Array.from(gradeSelect.options).map(opt => ({
                    value: opt.value,
                    textContent: opt.textContent
                }));
                console.log(`Stored ${window.originalGradeOptions['grade'].length} original options for grade`);
            }

            // Store options for the bulk upload form
            const bulkGradeSelect = document.getElementById('bulk_grade');
            if (bulkGradeSelect) {
                window.originalGradeOptions['bulk_grade'] = Array.from(bulkGradeSelect.options).map(opt => ({
                    value: opt.value,
                    textContent: opt.textContent
                }));
                console.log(`Stored ${window.originalGradeOptions['bulk_grade'].length} original options for bulk_grade`);
            }
        }

        // Function to fetch streams dynamically based on selected grade (for uploading marks)
        function fetchStreams() {
            const grade = document.getElementById('grade').value;
            const streamSelect = document.getElementById('stream');
            const formGroup = streamSelect.closest('.form-group');

            console.log('=== FETCH STREAMS DEBUG ===');
            console.log('fetchStreams called with grade:', grade);
            console.log('Stream select element:', streamSelect);
            console.log('Form group element:', formGroup);

            // Clear existing options
            streamSelect.innerHTML = '<option value="">Select Stream</option>';

            if (grade) {
                // Extract grade level from "Grade X" format
                const gradeLevel = grade.replace('Grade ', '');
                console.log('Extracted grade level:', gradeLevel);

                // Function to get the correct streams API endpoint based on current context
                function getStreamsEndpoint(grade) {
                    // Check if we're in headteacher universal context
                    if (window.location.pathname.includes('/universal/')) {
                        return `/universal/api/streams/${grade}`;
                    }
                    // Check if we're in headteacher context (admin routes)
                    else if (window.location.pathname.includes('/headteacher/') ||
                        window.location.pathname.includes('/admin/')) {
                        return `/admin/api/streams_by_level/${grade}`;
                    }
                    // Default to classteacher endpoint
                    return `/classteacher/get_streams_by_level/${grade}`;
                }

                // Function to extract streams from response data (handles different response formats)
                function extractStreamsFromResponse(data) {
                    // Handle different response formats
                    if (data.streams) {
                        return data.streams; // Admin blueprint format: {streams: [...]}
                    } else if (data.success && data.streams) {
                        return data.streams; // Universal blueprint format: {success: true, streams: [...]}
                    }
                    return []; // Fallback to empty array
                }

                const apiUrl = getStreamsEndpoint(gradeLevel);
                console.log('Making API call to:', apiUrl);

                // Fetch streams for this grade
                fetch(apiUrl, {
                    credentials: 'same-origin'
                })
                    .then(response => {
                        console.log('Response status:', response.status);
                        console.log('Response headers:', response.headers);

                        if (response.status === 401) {
                            console.error('Authentication required - user needs to login');
                            // Redirect to login if not authenticated
                            window.location.href = '/classteacher_login';
                            return;
                        }

                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        return response.json();
                    })
                    .then(data => {
                        if (!data) {
                            console.log('No data received (redirect case)');
                            return;
                        }

                        console.log('Received streams data:', data);
                        console.log('Data success:', data.success);
                        console.log('Data streams:', data.streams);
                        console.log('Number of streams:', data.streams ? data.streams.length : 0);

                        const streams = extractStreamsFromResponse(data);
                        if (streams && streams.length > 0) {
                            console.log('Processing streams...');

                            // Clear any existing options except the first one (to avoid duplicates)
                            while (streamSelect.options.length > 1) {
                                streamSelect.removeChild(streamSelect.lastChild);
                            }

                            // Create a Set to track unique stream names and avoid duplicates
                            const addedStreams = new Set();

                            streams.forEach((stream, index) => {
                                const streamName = stream.name;
                                const streamValue = `Stream ${streamName}`;

                                // Only add if we haven't seen this stream name before
                                if (!addedStreams.has(streamName)) {
                                    console.log(`Adding stream ${index + 1}: ${streamName} (ID: ${stream.id})`);
                                    const option = document.createElement('option');
                                    option.value = streamValue;
                                    option.textContent = streamValue;
                                    streamSelect.appendChild(option);
                                    addedStreams.add(streamName);
                                } else {
                                    console.log(`Skipping duplicate stream: ${streamName}`);
                                }
                            });
                            formGroup.classList.add('success');
                            formGroup.classList.remove('error');
                            console.log('Successfully populated streams');
                            console.log('Final stream select options:', streamSelect.options.length);
                            console.log('Unique streams added:', Array.from(addedStreams));
                        } else {
                            console.error('No streams found or error in response:', data);
                            formGroup.classList.add('error');

                            // Show helpful error message
                            if (data && data.message) {
                                console.error('API Error:', data.message);
                                // Show error message to user
                                alert(`Error loading streams: ${data.message}`);
                            } else {
                                console.error('Unknown error or no streams available');
                                alert('No streams available for the selected grade');
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching streams:', error);
                        // Fallback: try the other endpoint if first one fails
                        const fallbackEndpoint = gradeLevel ?
                            (apiUrl.includes('/universal/') ?
                                `/classteacher/get_streams_by_level/${gradeLevel}` :
                                apiUrl.includes('/admin/') ?
                                `/classteacher/get_streams_by_level/${gradeLevel}` :
                                `/universal/api/streams/${gradeLevel}`) : null;

                        if (fallbackEndpoint) {
                            console.log("Classteacher.html trying fallback endpoint:", fallbackEndpoint);
                            fetch(fallbackEndpoint, {
                                credentials: 'same-origin'
                            })
                                .then(response => response.json())
                                .then(data => {
                                    const streams = extractStreamsFromResponse(data);
                                    if (streams && streams.length > 0) {
                                        // Clear any existing options except the first one
                                        while (streamSelect.options.length > 1) {
                                            streamSelect.removeChild(streamSelect.lastChild);
                                        }

                                        const addedStreams = new Set();
                                        streams.forEach((stream, index) => {
                                            const streamName = stream.name;
                                            const streamValue = `Stream ${streamName}`;

                                            if (!addedStreams.has(streamName)) {
                                                const option = document.createElement('option');
                                                option.value = streamValue;
                                                option.textContent = streamValue;
                                                streamSelect.appendChild(option);
                                                addedStreams.add(streamName);
                                            }
                                        });
                                        formGroup.classList.add('success');
                                        formGroup.classList.remove('error');
                                    }
                                })
                                .catch(fallbackError => {
                                    console.error('Classteacher.html fallback endpoint also failed:', fallbackError);
                                    formGroup.classList.add('error');
                                    alert(`Network error: ${error.message}`);
                                });
                        } else {
                            formGroup.classList.add('error');
                            alert(`Network error: ${error.message}`);
                        }
                    });
            } else {
                console.log('No grade selected, clearing form group success state');
                formGroup.classList.remove('success');
            }
        }

        // Function to fetch subjects for a specific education level
        function fetchSubjectsForEducationLevel(educationLevel) {
            // This function will be called when the education level changes
            // or when the grade changes (via fetchStreams)
            fetch(`/classteacher/get_subjects_by_education_level/${educationLevel}`, {
                credentials: 'same-origin'
            })
                .then(response => response.json())
                .then(data => {
                    console.log('Fetched subjects for education level:', educationLevel, data);
                    // We don't need to update the UI here, but we can store the subjects
                    // for later use when the form is submitted
                    window.availableSubjects = data.subjects;
                })
                .catch(error => {
                    console.error('Error fetching subjects:', error);
                });
        }

        // Function to update percentage in real-time as marks are entered in the bulk upload form
        function updateBulkPercentage(inputElement, studentSubjectId) {
            const totalMarksInput = document.getElementsByName('total_marks')[0];
            if (!totalMarksInput) return;

            const totalMarks = parseInt(totalMarksInput.value) || 100;
            const mark = parseInt(inputElement.value) || 0;

            // Calculate percentage and ensure it doesn't exceed 100%
            let percentage = (mark / totalMarks) * 100;
            if (percentage > 100) {
                percentage = 100;
                inputElement.value = totalMarks; // Cap the raw mark
            }

            // Find the corresponding percentage cell
            const percentageCell = document.getElementById(`percentage_${studentSubjectId}`);

            if (percentageCell) {
                percentageCell.textContent = percentage.toFixed(1) + '%';

                // Update visual indicator
                updatePerformanceIndicator(percentageCell, percentage);
            }
        }

        // Function to check stream status for grade marksheet
        function checkStreamStatus() {
            const grade = document.getElementById('stream_grade').value;
            const term = document.getElementById('stream_term').value;
            const assessmentType = document.getElementById('stream_assessment_type').value;
            const statusContainer = document.getElementById('stream-status-container');
            const statusList = document.getElementById('stream-status-list');
            const previewBtn = document.getElementById('preview-marksheet-btn');
            const downloadBtn = document.getElementById('download-marksheet-btn');

            // Clear the status list
            statusList.innerHTML = '';

            if (grade && term && assessmentType) {
                statusContainer.style.display = 'block';

                // Add loading indicator
                statusList.innerHTML = '<p>Checking stream status...</p>';

                // Fetch stream status from the server
                fetch(`/classteacher/api/check_stream_status/${grade}/${term}/${assessmentType}`, {
                    credentials: 'same-origin'
                })
                    .then(response => response.json())
                    .then(data => {
                        statusList.innerHTML = '';
                        let allStreamsReady = true;

                        if (data.streams && data.streams.length > 0) {
                            data.streams.forEach(stream => {
                                const statusItem = document.createElement('div');
                                statusItem.className = 'stream-status-item';

                                const indicator = document.createElement('span');
                                indicator.className = `stream-status-indicator ${stream.has_report ? 'status-ready' : 'status-pending'}`;

                                const text = document.createElement('span');
                                text.textContent = `${stream.name}: ${stream.has_report ? 'Report Ready' : 'Report Missing'}`;

                                statusItem.appendChild(indicator);
                                statusItem.appendChild(text);
                                statusList.appendChild(statusItem);

                                if (!stream.has_report) {
                                    allStreamsReady = false;
                                }
                            });

                            // Enable or disable buttons based on status
                            previewBtn.disabled = !allStreamsReady;
                            downloadBtn.disabled = !allStreamsReady;

                            if (!allStreamsReady) {
                                const warningText = document.createElement('p');
                                warningText.style.color = '#dc3545';
                                warningText.style.marginTop = '10px';
                                warningText.innerHTML = '<strong>Note:</strong> All streams must have reports generated before creating a grade marksheet.';
                                statusList.appendChild(warningText);
                            } else {
                                const successText = document.createElement('p');
                                successText.style.color = '#28a745';
                                successText.style.marginTop = '10px';
                                successText.innerHTML = '<strong>Ready!</strong> All stream reports are available. You can now generate the grade marksheet.';
                                statusList.appendChild(successText);
                            }
                        } else {
                            statusList.innerHTML = '<p>No streams found for this grade.</p>';
                            previewBtn.disabled = true;
                            downloadBtn.disabled = true;
                        }
                    })
                    .catch(error => {
                        console.error('Error checking stream status:', error);
                        statusList.innerHTML = '<p style="color: #dc3545;">Error checking stream status. Please try again.</p>';
                        previewBtn.disabled = true;
                        downloadBtn.disabled = true;
                    });
            } else {
                statusContainer.style.display = 'none';
                previewBtn.disabled = true;
                downloadBtn.disabled = true;
            }
        }

        // Function to update grades based on education level for bulk upload form
        function updateBulkGrades() {
            const educationLevel = document.getElementById('bulk_education_level').value;
            const gradeSelect = document.getElementById('bulk_grade');
            const formGroup = document.getElementById('bulk_education_level').closest('.form-group');

            if (educationLevel) {
                formGroup.classList.add('success');
                formGroup.classList.remove('error');

                // Filter grades based on education level
                filterGradesByEducationLevel(educationLevel, gradeSelect);

                // Clear the stream dropdown since grade has changed
                const streamSelect = document.getElementById('bulk_stream');
                streamSelect.innerHTML = '<option value="">Select Stream</option>';

                console.log("Updated bulk form grades for education level:", educationLevel);
            } else {
                formGroup.classList.remove('success');

                // If no education level is selected, restore all grade options
                if (window.originalGradeOptions && window.originalGradeOptions['bulk_grade']) {
                    restoreOriginalGradeOptions(gradeSelect);
                }
            }
        }

        // Function to fetch streams for bulk upload form
        function fetchBulkStreams() {
            const grade = document.getElementById('bulk_grade').value;
            const streamSelect = document.getElementById('bulk_stream');
            const formGroup = streamSelect.closest('.form-group');

            console.log('fetchBulkStreams called with grade:', grade);

            // Clear existing options
            streamSelect.innerHTML = '<option value="">Select Stream</option>';

            if (grade) {
                // Extract grade level from "Grade X" format
                const gradeLevel = grade.replace('Grade ', '');
                console.log('Extracted bulk grade level:', gradeLevel);

                // Function to get the correct streams API endpoint based on current context
                function getBulkStreamsEndpoint(grade) {
                    // Check if we're in headteacher universal context
                    if (window.location.pathname.includes('/universal/')) {
                        return `/universal/api/streams/${grade}`;
                    }
                    // Check if we're in headteacher context (admin routes)
                    else if (window.location.pathname.includes('/headteacher/') ||
                        window.location.pathname.includes('/admin/')) {
                        return `/admin/api/streams_by_level/${grade}`;
                    }
                    // Default to classteacher endpoint
                    return `/classteacher/get_streams_by_level/${grade}`;
                }

                // Function to extract streams from response data (handles different response formats)
                function extractBulkStreamsFromResponse(data) {
                    // Handle different response formats
                    if (data.streams) {
                        return data.streams; // Admin blueprint format: {streams: [...]}
                    } else if (data.success && data.streams) {
                        return data.streams; // Universal blueprint format: {success: true, streams: [...]}
                    }
                    return []; // Fallback to empty array
                }

                const bulkApiUrl = getBulkStreamsEndpoint(gradeLevel);
                console.log('Making bulk API call to:', bulkApiUrl);

                fetch(bulkApiUrl, {
                    credentials: 'same-origin'
                })
                    .then(response => {
                        console.log('Bulk streams response status:', response.status);
                        if (response.status === 401) {
                            console.error('Authentication required - user needs to login');
                            // Redirect to login if not authenticated
                            window.location.href = '/classteacher_login';
                            return;
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (!data) return; // Handle redirect case

                        console.log('Received bulk streams data:', data);
                        const streams = extractBulkStreamsFromResponse(data);
                        if (streams && streams.length > 0) {
                            // Clear any existing options except the first one (to avoid duplicates)
                            while (streamSelect.options.length > 1) {
                                streamSelect.removeChild(streamSelect.lastChild);
                            }

                            // Create a Set to track unique stream names and avoid duplicates
                            const addedStreams = new Set();

                            streams.forEach(stream => {
                                const streamName = stream.name;
                                const streamValue = `Stream ${streamName}`;

                                // Only add if we haven't seen this stream name before
                                if (!addedStreams.has(streamName)) {
                                    const option = document.createElement('option');
                                    option.value = streamValue;
                                    option.textContent = streamValue;
                                    streamSelect.appendChild(option);
                                    addedStreams.add(streamName);
                                }
                            });
                            formGroup.classList.add('success');
                            formGroup.classList.remove('error');
                            console.log('Successfully populated bulk streams');
                        } else {
                            console.error('No bulk streams found or error in response:', data);
                            formGroup.classList.add('error');

                            // Show helpful error message
                            if (data && data.message) {
                                console.error('API Error:', data.message);
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching bulk streams:', error);
                        // Fallback: try the other endpoint if first one fails
                        const fallbackEndpoint = gradeLevel ?
                            (bulkApiUrl.includes('/universal/') ?
                                `/classteacher/get_streams_by_level/${gradeLevel}` :
                                bulkApiUrl.includes('/admin/') ?
                                `/classteacher/get_streams_by_level/${gradeLevel}` :
                                `/universal/api/streams/${gradeLevel}`) : null;

                        if (fallbackEndpoint) {
                            console.log("Bulk streams trying fallback endpoint:", fallbackEndpoint);
                            fetch(fallbackEndpoint, {
                                credentials: 'same-origin'
                            })
                                .then(response => response.json())
                                .then(data => {
                                    const streams = extractBulkStreamsFromResponse(data);
                                    if (streams && streams.length > 0) {
                                        // Clear any existing options except the first one
                                        while (streamSelect.options.length > 1) {
                                            streamSelect.removeChild(streamSelect.lastChild);
                                        }

                                        const addedStreams = new Set();
                                        streams.forEach(stream => {
                                            const streamName = stream.name;
                                            const streamValue = `Stream ${streamName}`;

                                            if (!addedStreams.has(streamName)) {
                                                const option = document.createElement('option');
                                                option.value = streamValue;
                                                option.textContent = streamValue;
                                                streamSelect.appendChild(option);
                                                addedStreams.add(streamName);
                                            }
                                        });
                                        formGroup.classList.add('success');
                                        formGroup.classList.remove('error');
                                    }
                                })
                                .catch(fallbackError => {
                                    console.error('Bulk streams fallback endpoint also failed:', fallbackError);
                                    formGroup.classList.add('error');
                                });
                        } else {
                            formGroup.classList.add('error');
                        }
                    });
            } else {
                formGroup.classList.remove('success');
            }
        }

        // Function to sort reports
        function sortReports(sortBy) {
            // Get current URL parameters
            const urlParams = new URLSearchParams(window.location.search);

            // Update or add the sort parameter
            urlParams.set('sort', sortBy);

            // Redirect to the updated URL
            window.location.href = `${window.location.pathname}?${urlParams.toString()}`;
        }

        // Function to filter reports
        function filterReports() {
            // Get filter values
            const filterGrade = document.getElementById('filter-grade').value;
            const filterTerm = document.getElementById('filter-term').value;

            // Get current URL parameters
            const urlParams = new URLSearchParams(window.location.search);

            // Update or add the filter parameters
            if (filterGrade) {
                urlParams.set('filter_grade', filterGrade);
            } else {
                urlParams.delete('filter_grade');
            }

            if (filterTerm) {
                urlParams.set('filter_term', filterTerm);
            } else {
                urlParams.delete('filter_term');
            }

            // Redirect to the updated URL
            window.location.href = `${window.location.pathname}?${urlParams.toString()}`;
        }

        // Function to reset filters
        function resetFilters() {
            window.location.href = window.location.pathname;
        }

        // Function to download marks template
        function downloadTemplate() {
            const educationLevel = document.getElementById('bulk_education_level').value;
            const grade = document.getElementById('bulk_grade').value;
            const stream = document.getElementById('bulk_stream').value;
            const term = document.getElementById('bulk_term').value;
            const assessmentType = document.getElementById('bulk_assessment_type').value;

            let url = '/classteacher/download_marks_template?format=xlsx';

            if (educationLevel) {
                url += `&education_level=${educationLevel}`;
            }

            if (grade && stream) {
                url += `&grade=${grade}&stream=${stream}`;

                if (term) {
                    url += `&term=${term}`;
                }

                if (assessmentType) {
                    url += `&assessment_type=${assessmentType}`;
                }
            }

            window.location.href = url;
        }

        // Tab switching functionality
        function switchMainTab(tabId) {
            console.log(`=== SWITCHING TO TAB: ${tabId} ===`);

            // Hide all tab content
            document.querySelectorAll('.tab-content-container').forEach(function(tab) {
                tab.classList.remove('active');
            });

            // Deactivate all navigation items
            document.querySelectorAll('.nav-item').forEach(function(navItem) {
                navItem.classList.remove('active');
            });

            // Deactivate all tab buttons
            document.querySelectorAll('.tab-button').forEach(function(tab) {
                tab.classList.remove('active');
            });

            // Show the selected tab content
            const targetTab = document.getElementById(tabId + '-tab');
            if (targetTab) {
                targetTab.classList.add('active');
                console.log(`Successfully activated tab: ${tabId}`);
            } else {
                console.error(`Tab not found: ${tabId}-tab`);
            }

            // Activate the corresponding navigation item
            const navItem = document.querySelector(`[data-feature="${tabId}"]`);
            if (navItem) {
                navItem.classList.add('active');
            }

            // Activate the corresponding tab button
            const tabButton = document.querySelector(`.tab-button[onclick*="${tabId}"]`);
            if (tabButton) {
                tabButton.classList.add('active');
            }

            // Store the active tab in session storage so it persists during form submissions
            sessionStorage.setItem('activeTab', tabId);

            // Auto-scroll to upload marks section when upload-marks tab is selected
            if (tabId === 'upload-marks') {
                setTimeout(() => {
                    const uploadSection = document.getElementById('upload-marks-section');
                    if (uploadSection) {
                        uploadSection.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start',
                            inline: 'nearest'
                        });
                        console.log('Auto-scrolled to upload marks section');
                    }
                }, 300); // Small delay to ensure tab transition is complete
            }
        }

        // Function to restore the active tab after page reload
        function restoreActiveTab() {
            // Only restore from session storage if we don't have a server-side active tab
            const serverActiveTab = "{{ active_tab }}";
            if (serverActiveTab === "recent-reports") {
                const activeTab = sessionStorage.getItem('activeTab');
                if (activeTab) {
                    switchMainTab(activeTab);
                }
            }
        }

        // Management Options Filter and Search Functionality
        function initializeManagementOptions() {
            const filterButtons = document.querySelectorAll('.filter-btn');
            const managementCards = document.querySelectorAll('.management-option-card');
            const searchInput = document.getElementById('management-search');

            // Filter by category
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const filter = this.getAttribute('data-filter');

                    // Update active button
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    // Filter cards
                    managementCards.forEach(card => {
                        const category = card.getAttribute('data-category');
                        if (filter === 'all' || category === filter) {
                            card.style.display = 'flex';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            });

            // Search functionality
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();

                    managementCards.forEach(card => {
                        const title = card.querySelector('h4').textContent.toLowerCase();
                        const description = card.querySelector('p').textContent.toLowerCase();

                        if (title.includes(searchTerm) || description.includes(searchTerm)) {
                            card.style.display = 'flex';
                        } else {
                            card.style.display = 'none';
                        }
                    });

                    // Reset filter buttons when searching
                    if (searchTerm) {
                        filterButtons.forEach(btn => btn.classList.remove('active'));
                    } else {
                        // Restore "All" filter when search is cleared
                        filterButtons[0].classList.add('active');
                        managementCards.forEach(card => {
                            card.style.display = 'flex';
                        });
                    }
                });
            }
        }

        // Initialize form validations on page load
        document.addEventListener('DOMContentLoaded', () => {
            console.log("DOM loaded, initializing forms");

            // Auto-scroll to upload form if students are being shown
            {% if show_students %}
            console.log("Students form is visible - auto-scrolling to upload form");
            setTimeout(() => {
                const pupilsList = document.getElementById('pupils-list');
                if (pupilsList) {
                    pupilsList.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start',
                        inline: 'nearest'
                    });
                    console.log("Scrolled to pupils-list");
                }
            }, 500); // Small delay to ensure page is fully rendered
            {% endif %}

            // Initialize management options functionality
            initializeManagementOptions();

            // Initialize enhanced navigation system
            initializeEnhancedNavigation();

            // Store original grade options for all grade selects
            storeOriginalGradeOptions();

            // Add direct event listeners to education level dropdowns
            const educationLevel = document.getElementById('education_level');
            if (educationLevel) {
                educationLevel.addEventListener('change', function() {
                    console.log("Education level changed to:", this.value);
                    if (this.value) {
                        const gradeSelect = document.getElementById('grade');
                        filterGradesByEducationLevel(this.value, gradeSelect);
                    }
                });

                // Initialize main form if education level is already selected
                if (educationLevel.value) {
                    console.log("Initializing main form with education level:", educationLevel.value);
                    const gradeSelect = document.getElementById('grade');
                    filterGradesByEducationLevel(educationLevel.value, gradeSelect);
                }
            }

            // Initialize bulk upload form if it exists
            const bulkEducationLevel = document.getElementById('bulk_education_level');
            if (bulkEducationLevel) {
                bulkEducationLevel.addEventListener('change', function() {
                    console.log("Bulk education level changed to:", this.value);
                    if (this.value) {
                        const bulkGradeSelect = document.getElementById('bulk_grade');
                        filterGradesByEducationLevel(this.value, bulkGradeSelect);
                    }
                });

                // Initialize bulk form if education level is already selected
                if (bulkEducationLevel.value) {
                    console.log("Initializing bulk form with education level:", bulkEducationLevel.value);
                    const bulkGradeSelect = document.getElementById('bulk_grade');
                    filterGradesByEducationLevel(bulkEducationLevel.value, bulkGradeSelect);
                }
            }

            // Initialize streams if grade is already selected
            const gradeSelect = document.getElementById('grade');
            if (gradeSelect && gradeSelect.value) {
                console.log("Initializing streams for grade:", gradeSelect.value);
                fetchStreams();
            }

            // Restore active tab from session storage if needed
            restoreActiveTab();

            // Add scroll functionality to upload buttons
            const uploadBtn = document.getElementById('upload-btn');
            const bulkUploadBtn = document.getElementById('bulk-upload-btn');
            const submitMarksBtn = document.getElementById('submit-marks-btn');

            if (uploadBtn) {
                uploadBtn.addEventListener('click', function(e) {
                    console.log('Upload button clicked - will scroll after form submission');
                    // The scroll will happen automatically when show_students becomes true
                    // due to the Jinja template logic we added above
                });
            }

            if (bulkUploadBtn) {
                bulkUploadBtn.addEventListener('click', function(e) {
                    console.log('Bulk upload button clicked - will scroll after form submission');
                    // The scroll will happen automatically when show_students becomes true
                    // due to the Jinja template logic we added above
                });
            }

            // Add event listener for submit marks button
            if (submitMarksBtn) {
                submitMarksBtn.addEventListener('click', function(e) {
                    console.log('Submit marks button clicked - validating form...');

                    // Run validation but don't prevent default submission
                    const hasSelectedSubjects = addSelectedSubjectsToForm();

                    if (!hasSelectedSubjects) {
                        e.preventDefault(); // Only prevent if validation fails
                        showValidationError('Please select at least one subject before submitting marks.');
                        return false;
                    }

                    // Allow normal form submission to proceed
                    console.log('Form validation passed, allowing normal submission...');
                });
            }

            // Add event listeners for grade marksheet form fields
            document.getElementById('stream_grade').addEventListener('change', checkStreamStatus);
            document.getElementById('stream_term').addEventListener('change', checkStreamStatus);
            document.getElementById('stream_assessment_type').addEventListener('change', checkStreamStatus);

            // Close print options dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!event.target.matches('.dropdown-toggle') && !event.target.closest('.dropdown-menu')) {
                    document.querySelectorAll('.dropdown-menu').forEach(menu => {
                        menu.style.display = 'none';
                    });
                }
            });

            // Tab functionality
            const tabBtns = document.querySelectorAll('.tab-btn');
            const tabPanes = document.querySelectorAll('.tab-pane');

            tabBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // Remove active class from all buttons and panes
                    tabBtns.forEach(b => b.classList.remove('active'));
                    tabPanes.forEach(p => p.classList.remove('active'));

                    // Add active class to clicked button and corresponding pane
                    btn.classList.add('active');
                    const tabId = btn.getAttribute('data-tab');
                    document.getElementById(tabId).classList.add('active');
                });
            });

            // Initialize subject visibility based on checkboxes
            document.querySelectorAll('.subject-checkbox').forEach(checkbox => {
                if (checkbox && checkbox.dataset.subjectId) {
                    toggleSubjectVisibility(checkbox, checkbox.dataset.subjectId);
                }
            });
        });

        // Function to toggle subject visibility in the table (CORRECTED VERSION)
        function toggleSubjectVisibility(checkbox, subjectId) {
            console.log(`🔧 Toggling subject visibility for ID: ${subjectId}, checked: ${checkbox.checked}`);

            // Get the correct table cells and headers for this subject
            const subjectHeaders = document.querySelectorAll(`th[data-subject-id="${subjectId}"]`);
            const subjectCells = document.querySelectorAll(`td[data-subject-id="${subjectId}"]`);
            const subjectItem = checkbox.closest('.subject-mark-item');

            console.log(`Found ${subjectHeaders.length} headers and ${subjectCells.length} cells for subject ${subjectId}`);

            if (checkbox.checked) {
                // Show the subject
                subjectHeaders.forEach(header => {
                    header.style.display = '';
                    console.log(`✅ Showed header for subject ${subjectId}`);
                });

                subjectCells.forEach(cell => {
                    cell.style.display = '';
                    console.log(`✅ Showed cell for subject ${subjectId}`);
                });

                if (subjectItem) {
                    subjectItem.classList.remove('disabled');
                    console.log(`✅ Enabled subject item for ${subjectId}`);
                }
            } else {
                // Hide the subject
                subjectHeaders.forEach(header => {
                    header.style.display = 'none';
                    console.log(`❌ Hidden header for subject ${subjectId}`);
                });

                subjectCells.forEach(cell => {
                    cell.style.display = 'none';
                    console.log(`❌ Hidden cell for subject ${subjectId}`);
                });

                if (subjectItem) {
                    subjectItem.classList.add('disabled');
                    console.log(`❌ Disabled subject item for ${subjectId}`);
                }
            }
        }

        // Function to select or deselect all subjects
        function selectAllSubjects(select) {
            const checkboxes = document.querySelectorAll('.subject-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = select;
                if (checkbox.dataset.subjectId) {
                    toggleSubjectVisibility(checkbox, checkbox.dataset.subjectId);
                }
            });
        }

        // Modern Tab System
        document.addEventListener('DOMContentLoaded', function() {
            // Handle modern tab buttons
            const modernTabButtons = document.querySelectorAll('.tab-button');
            const modernTabPanes = document.querySelectorAll('.tab-pane');

            modernTabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const targetTab = this.getAttribute('data-tab');

                    // Remove active class from all buttons and panes
                    modernTabButtons.forEach(btn => btn.classList.remove('active'));
                    modernTabPanes.forEach(pane => pane.classList.remove('active'));

                    // Add active class to clicked button and corresponding pane
                    this.classList.add('active');
                    const targetPane = document.getElementById(targetTab);
                    if (targetPane) {
                        targetPane.classList.add('active');
                    }
                });
            });

            // Handle filter buttons in management section
            const filterButtons = document.querySelectorAll('[data-filter]');
            const managementCards = document.querySelectorAll('[data-category]');

            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const filter = this.getAttribute('data-filter');

                    // Update active filter button
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    // Filter management cards
                    managementCards.forEach(card => {
                        if (filter === 'all' || card.getAttribute('data-category') === filter) {
                            card.style.display = 'block';
                            card.style.animation = 'fadeIn 0.3s ease-in-out';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            });

            // Search functionality for management options
            const searchInput = document.getElementById('management-search');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();

                    managementCards.forEach(card => {
                        const title = card.querySelector('.quick-action-title')?.textContent.toLowerCase() || '';
                        const desc = card.querySelector('.quick-action-desc')?.textContent.toLowerCase() || '';

                        if (title.includes(searchTerm) || desc.includes(searchTerm)) {
                            card.style.display = 'block';
                            card.style.animation = 'fadeIn 0.3s ease-in-out';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            }

            // Add loading states to buttons
            const modernButtons = document.querySelectorAll('.modern-btn');
            modernButtons.forEach(button => {
                button.addEventListener('click', function() {
                    if (this.type === 'submit') {
                        this.classList.add('loading');
                        setTimeout(() => {
                            this.classList.remove('loading');
                        }, 2000);
                    }
                });
            });

            // Load permission status on page load
            loadPermissionStatus();

            // Initialize assignment management
            initializeAssignmentManagement();

            // Note: Analytics dashboard is initialized only on the dedicated analytics page
        });

        // Get CSRF token
        function getCSRFToken() {
            return document.querySelector('meta[name=csrf-token]').getAttribute('content');
        }

        // Permission Status Functions
        function loadPermissionStatus() {
            const container = document.getElementById('permission-status-content');

            fetch('/permission/my_permissions_api', {
                credentials: 'same-origin'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayPermissionStatus(data);
                    } else {
                        showPermissionError(data.message);
                    }
                })
                .catch(error => {
                    showPermissionError('Failed to load permission status');
                });
        }

        function displayPermissionStatus(data) {
            const container = document.getElementById('permission-status-content');

            if (data.is_headteacher) {
                container.innerHTML = `
                    <div style="display: flex; align-items: center; gap: var(--space-4);">
                        <div style="background: linear-gradient(135deg, #059669 0%, #047857 100%); color: white; padding: var(--space-3); border-radius: 50%; width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-crown" style="font-size: 1.5rem;"></i>
                        </div>
                        <div>
                            <h3 style="margin: 0; color: var(--primary-color);">Full Administrative Access</h3>
                            <p style="margin: 0; color: var(--gray-600);">${data.message}</p>
                        </div>
                        <div style="margin-left: auto;">
                            <a href="/permission/manage" class="modern-btn btn-primary">
                                <i class="fas fa-cogs"></i>
                                Manage Permissions
                            </a>
                        </div>
                    </div>
                `;
            } else if (data.has_permissions && data.permissions.length > 0) {
                let permissionsHtml = data.permissions.map(perm => `
                    <div class="modern-badge badge-success" style="margin: var(--space-1);">
                        <i class="fas fa-check-circle"></i>
                        ${perm.display_name}
                    </div>
                `).join('');

                container.innerHTML = `
                    <div style="display: flex; align-items: center; gap: var(--space-4);">
                        <div style="background: linear-gradient(135deg, #059669 0%, #047857 100%); color: white; padding: var(--space-3); border-radius: 50%; width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-key" style="font-size: 1.5rem;"></i>
                        </div>
                        <div style="flex: 1;">
                            <h3 style="margin: 0; color: var(--primary-color);">Active Permissions</h3>
                            <p style="margin: var(--space-1) 0; color: var(--gray-600);">${data.message}</p>
                            <div style="display: flex; flex-wrap: wrap; gap: var(--space-1); margin-top: var(--space-2);">
                                ${permissionsHtml}
                            </div>
                        </div>
                    </div>
                `;
            } else {
                container.innerHTML = `
                    <div style="display: flex; align-items: center; gap: var(--space-4);">
                        <div style="background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); color: white; padding: var(--space-3); border-radius: 50%; width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-lock" style="font-size: 1.5rem;"></i>
                        </div>
                        <div style="flex: 1;">
                            <h3 style="margin: 0; color: #dc2626;">No Class Permissions</h3>
                            <p style="margin: 0; color: var(--gray-600);">You don't have permission to manage any classes. Contact the headteacher to request access.</p>
                        </div>
                        <div>
                            <button onclick="requestPermission()" class="modern-btn btn-outline">
                                <i class="fas fa-paper-plane"></i>
                                Request Access
                            </button>
                        </div>
                    </div>
                `;
            }
        }

        function showPermissionError(message) {
            const container = document.getElementById('permission-status-content');
            container.innerHTML = `
                <div style="display: flex; align-items: center; gap: var(--space-4);">
                    <div style="background: linear-gradient(135deg, #d97706 0%, #b45309 100%); color: white; padding: var(--space-3); border-radius: 50%; width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 1.5rem;"></i>
                    </div>
                    <div style="flex: 1;">
                        <h3 style="margin: 0; color: #d97706;">Permission Status Error</h3>
                        <p style="margin: 0; color: var(--gray-600);">${message}</p>
                    </div>
                    <div>
                        <button onclick="refreshPermissionStatus()" class="modern-btn btn-outline">
                            <i class="fas fa-sync-alt"></i>
                            Retry
                        </button>
                    </div>
                </div>
            `;
        }

        function refreshPermissionStatus() {
            const container = document.getElementById('permission-status-content');
            container.innerHTML = `
                <div style="text-align: center; color: var(--gray-500);">
                    <i class="fas fa-spinner fa-spin" style="font-size: 1.5rem; margin-bottom: var(--space-2);"></i>
                    <p>Refreshing permission status...</p>
                </div>
            `;
            loadPermissionStatus();
        }

        function requestPermission() {
            // Show the permission request modal
            showPermissionRequestModal();
        }

        function showPermissionRequestModal() {
            // Create modal HTML
            const modalHTML = `
                <div id="permissionRequestModal" style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 1000;
                ">
                    <div style="
                        background: white;
                        border-radius: var(--radius-lg);
                        padding: var(--space-6);
                        max-width: 500px;
                        width: 90%;
                        max-height: 80vh;
                        overflow-y: auto;
                        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
                    ">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--space-4);">
                            <h3 style="margin: 0; color: var(--primary-color);">
                                <i class="fas fa-paper-plane"></i>
                                Request Class Permission
                            </h3>
                            <button onclick="closePermissionRequestModal()" style="
                                background: none;
                                border: none;
                                font-size: 1.5rem;
                                color: var(--gray-400);
                                cursor: pointer;
                                padding: var(--space-1);
                            ">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <form id="permissionRequestForm">
                            <div style="margin-bottom: var(--space-4);">
                                <label style="display: block; margin-bottom: var(--space-2); font-weight: 600; color: var(--gray-700);">
                                    Education Level *
                                </label>
                                <select id="requestEducationLevel" required style="
                                    width: 100%;
                                    padding: var(--space-3);
                                    border: 1px solid var(--gray-300);
                                    border-radius: var(--radius-md);
                                    font-size: 1rem;
                                ">
                                    <option value="">Select education level...</option>
                                    <option value="lower_primary">Lower Primary (PP1, PP2, Grade 1-3)</option>
                                    <option value="upper_primary">Upper Primary (Grade 4-6)</option>
                                    <option value="junior_secondary">Junior Secondary (Grade 7-9)</option>
                                </select>
                            </div>

                            <div style="margin-bottom: var(--space-4);">
                                <label style="display: block; margin-bottom: var(--space-2); font-weight: 600; color: var(--gray-700);">
                                    Grade/Class *
                                </label>
                                <select id="requestGrade" required style="
                                    width: 100%;
                                    padding: var(--space-3);
                                    border: 1px solid var(--gray-300);
                                    border-radius: var(--radius-md);
                                    font-size: 1rem;
                                ">
                                    <option value="">Select a grade...</option>
                                </select>
                            </div>

                            <div style="margin-bottom: var(--space-4);">
                                <label style="display: block; margin-bottom: var(--space-2); font-weight: 600; color: var(--gray-700);">
                                    Stream (if applicable)
                                </label>
                                <select id="requestStream" style="
                                    width: 100%;
                                    padding: var(--space-3);
                                    border: 1px solid var(--gray-300);
                                    border-radius: var(--radius-md);
                                    font-size: 1rem;
                                ">
                                    <option value="">No specific stream</option>
                                </select>
                            </div>

                            <div style="margin-bottom: var(--space-4);">
                                <label style="display: block; margin-bottom: var(--space-2); font-weight: 600; color: var(--gray-700);">
                                    Reason for Request *
                                </label>
                                <textarea id="requestReason" required placeholder="Please explain why you need access to this class..." style="
                                    width: 100%;
                                    padding: var(--space-3);
                                    border: 1px solid var(--gray-300);
                                    border-radius: var(--radius-md);
                                    font-size: 1rem;
                                    min-height: 100px;
                                    resize: vertical;
                                "></textarea>
                            </div>

                            <div style="display: flex; gap: var(--space-3); justify-content: flex-end;">
                                <button type="button" onclick="closePermissionRequestModal()" style="
                                    padding: var(--space-3) var(--space-4);
                                    border: 1px solid var(--gray-300);
                                    background: white;
                                    color: var(--gray-700);
                                    border-radius: var(--radius-md);
                                    cursor: pointer;
                                    font-size: 1rem;
                                ">
                                    Cancel
                                </button>
                                <button type="submit" style="
                                    padding: var(--space-3) var(--space-4);
                                    border: none;
                                    background: var(--primary-color);
                                    color: white;
                                    border-radius: var(--radius-md);
                                    cursor: pointer;
                                    font-size: 1rem;
                                ">
                                    <i class="fas fa-paper-plane"></i>
                                    Submit Request
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            // Add modal to page
            document.body.insertAdjacentHTML('beforeend', modalHTML);

            // Load grades and streams
            loadGradesForRequest();

            // Handle form submission
            document.getElementById('permissionRequestForm').addEventListener('submit', handlePermissionRequest);
        }

        function closePermissionRequestModal() {
            const modal = document.getElementById('permissionRequestModal');
            if (modal) {
                modal.remove();
            }
        }

        function loadGradesForRequest() {
            // Load available grades
            fetch('/permission/class_structure', {
                credentials: 'same-origin'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const educationLevelSelect = document.getElementById('requestEducationLevel');
                        const gradeSelect = document.getElementById('requestGrade');
                        const streamSelect = document.getElementById('requestStream');

                        // Store the full structure for filtering
                        window.classStructure = data.structure;

                        // Education level mapping
                        const educationLevels = {
                            'lower_primary': ['PP1', 'PP2', 'Grade 1', 'Grade 2', 'Grade 3'],
                            'upper_primary': ['Grade 4', 'Grade 5', 'Grade 6'],
                            'junior_secondary': ['Grade 7', 'Grade 8', 'Grade 9']
                        };

                        // Handle education level change to filter grades
                        educationLevelSelect.addEventListener('change', function() {
                            gradeSelect.innerHTML = '<option value="">Select a grade...</option>';
                            streamSelect.innerHTML = '<option value="">No specific stream</option>';

                            if (this.value && educationLevels[this.value]) {
                                educationLevels[this.value].forEach(gradeName => {
                                    if (data.structure[gradeName]) {
                                        const option = document.createElement('option');
                                        option.value = gradeName;
                                        option.textContent = gradeName;
                                        gradeSelect.appendChild(option);
                                    }
                                });
                            }
                        });

                        // Handle grade change to update streams
                        gradeSelect.addEventListener('change', function() {
                            streamSelect.innerHTML = '<option value="">No specific stream</option>';

                            if (this.value && data.structure[this.value]) {
                                data.structure[this.value].forEach(stream => {
                                    const option = document.createElement('option');
                                    option.value = stream.name;
                                    option.textContent = stream.name;
                                    streamSelect.appendChild(option);
                                });
                            }
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading class structure:', error);
                    alert('Failed to load class structure. Please try again.');
                });
        }

        function handlePermissionRequest(event) {
            event.preventDefault();

            const grade = document.getElementById('requestGrade').value;
            const stream = document.getElementById('requestStream').value;
            const reason = document.getElementById('requestReason').value;

            if (!grade || !reason.trim()) {
                alert('Please fill in all required fields');
                return;
            }

            // Submit the request
            fetch('/permission/request', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCSRFToken(),
                },
                body: JSON.stringify({
                    grade_name: grade,
                    stream_name: stream || null,
                    reason: reason.trim()
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('✅ ' + data.message + '\\n\\nThe headteacher will review your request and respond accordingly.');
                    closePermissionRequestModal();
                    // Refresh permission status
                    loadPermissionStatus();
                } else {
                    alert('❌ ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error submitting request:', error);
                alert('❌ Failed to submit request. Please try again.');
            });
        }

        // ============================================================================
        // ASSIGNMENT MANAGEMENT SYSTEM
        // ============================================================================

        let assignmentData = {{ subject_assignments | tojson }};
        let filteredAssignments = [...assignmentData];
        let currentAssignmentPage = 1;
        let assignmentsPerPage = 20;
        let currentSortField = '';
        let currentSortDirection = 'asc';

        function initializeAssignmentManagement() {
            if (!assignmentData || assignmentData.length === 0) {
                return;
            }

            // Populate grade filter options
            const gradeFilter = document.getElementById('assignment-grade-filter');
            if (gradeFilter) {
                const uniqueGrades = [...new Set(assignmentData.map(a => a.grade_level))].sort();
                uniqueGrades.forEach(grade => {
                    const option = document.createElement('option');
                    option.value = grade;
                    option.textContent = grade;
                    gradeFilter.appendChild(option);
                });

                // Initial render
                renderAssignments();
            }
        }

        function filterAssignments() {
            const searchTerm = document.getElementById('assignment-search').value.toLowerCase();
            const gradeFilter = document.getElementById('assignment-grade-filter').value;
            const roleFilter = document.getElementById('assignment-role-filter').value;

            filteredAssignments = assignmentData.filter(assignment => {
                const matchesSearch = assignment.subject_name.toLowerCase().includes(searchTerm);
                const matchesGrade = !gradeFilter || assignment.grade_level === gradeFilter;
                const matchesRole = !roleFilter ||
                    (roleFilter === 'class_teacher' && assignment.is_class_teacher) ||
                    (roleFilter === 'subject_teacher' && !assignment.is_class_teacher);

                return matchesSearch && matchesGrade && matchesRole;
            });

            currentAssignmentPage = 1;
            renderAssignments();
        }

        function sortAssignments(field) {
            if (currentSortField === field) {
                currentSortDirection = currentSortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                currentSortField = field;
                currentSortDirection = 'asc';
            }

            filteredAssignments.sort((a, b) => {
                let aValue, bValue;

                switch (field) {
                    case 'subject':
                        aValue = a.subject_name;
                        bValue = b.subject_name;
                        break;
                    case 'grade':
                        aValue = a.grade_level;
                        bValue = b.grade_level;
                        break;
                    case 'role':
                        aValue = a.is_class_teacher ? 'Class Teacher' : 'Subject Teacher';
                        bValue = b.is_class_teacher ? 'Class Teacher' : 'Subject Teacher';
                        break;
                    default:
                        return 0;
                }

                if (currentSortDirection === 'asc') {
                    return aValue.localeCompare(bValue);
                } else {
                    return bValue.localeCompare(aValue);
                }
            });

            updateSortIcons(field);
            renderAssignments();
        }

        function updateSortIcons(activeField) {
            // Reset all sort icons
            document.querySelectorAll('[id^="sort-"]').forEach(icon => {
                icon.className = 'fas fa-sort';
            });

            // Update active field icon
            const activeIcon = document.getElementById(`sort-${activeField}`);
            if (activeIcon) {
                activeIcon.className = currentSortDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
            }
        }

        function updateAssignmentPagination() {
            const perPageSelect = document.getElementById('assignment-per-page');
            assignmentsPerPage = perPageSelect.value === 'all' ? filteredAssignments.length : parseInt(perPageSelect.value);
            currentAssignmentPage = 1;
            renderAssignments();
        }

        function renderAssignments() {
            const tbody = document.getElementById('assignments-tbody');
            if (!tbody) return;

            const startIndex = (currentAssignmentPage - 1) * assignmentsPerPage;
            const endIndex = assignmentsPerPage === filteredAssignments.length ?
                filteredAssignments.length :
                Math.min(startIndex + assignmentsPerPage, filteredAssignments.length);

            const pageAssignments = filteredAssignments.slice(startIndex, endIndex);

            // Render table rows
            tbody.innerHTML = pageAssignments.map(assignment => `
                <tr>
                    <td><strong>${assignment.subject_name}</strong></td>
                    <td><span class="modern-badge badge-info">${assignment.grade_level}</span></td>
                    <td><span class="modern-badge badge-success">${assignment.stream_name || 'All'}</span></td>
                    <td>
                        ${assignment.is_class_teacher ?
                            '<span class="modern-badge badge-primary">Class Teacher</span>' :
                            '<span class="modern-badge badge-warning">Subject Teacher</span>'
                        }
                    </td>
                </tr>
            `).join('');

            // Update pagination info
            const info = document.getElementById('assignment-info');
            if (info) {
                const totalPages = Math.ceil(filteredAssignments.length / assignmentsPerPage);

                if (filteredAssignments.length === 0) {
                    info.textContent = 'No assignments found';
                } else {
                    info.textContent = `Showing ${startIndex + 1}-${endIndex} of ${filteredAssignments.length} assignments`;
                }

                // Update pagination controls
                renderAssignmentPaginationControls(totalPages);
            }
        }

        function renderAssignmentPaginationControls(totalPages) {
            const controls = document.getElementById('assignment-controls');
            if (!controls) return;

            if (totalPages <= 1) {
                controls.innerHTML = '';
                return;
            }

            let controlsHTML = '';

            // Previous button
            if (currentAssignmentPage > 1) {
                controlsHTML += `
                    <button onclick="goToAssignmentPage(${currentAssignmentPage - 1})"
                            class="modern-btn btn-outline" style="padding: var(--space-2) var(--space-3); font-size: 0.8rem;">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                `;
            }

            // Page numbers
            const startPage = Math.max(1, currentAssignmentPage - 2);
            const endPage = Math.min(totalPages, currentAssignmentPage + 2);

            for (let i = startPage; i <= endPage; i++) {
                const isActive = i === currentAssignmentPage;
                controlsHTML += `
                    <button onclick="goToAssignmentPage(${i})"
                            class="modern-btn ${isActive ? 'btn-primary' : 'btn-outline'}"
                            style="padding: var(--space-2) var(--space-3); font-size: 0.8rem;">
                        ${i}
                    </button>
                `;
            }

            // Next button
            if (currentAssignmentPage < totalPages) {
                controlsHTML += `
                    <button onclick="goToAssignmentPage(${currentAssignmentPage + 1})"
                            class="modern-btn btn-outline" style="padding: var(--space-2) var(--space-3); font-size: 0.8rem;">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                `;
            }

            controls.innerHTML = controlsHTML;
        }

        function goToAssignmentPage(page) {
            currentAssignmentPage = page;
            renderAssignments();
        }
    </script>

    <!-- Note: Analytics Dashboard JavaScript is loaded only on the dedicated analytics page -->

    <!-- COMPREHENSIVE RIGHTMOST COLUMN VISIBILITY FIXES -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 APPLYING COMPREHENSIVE RIGHTMOST COLUMN FIXES...');

            // COMPREHENSIVE INPUT FIELD ACCESSIBILITY FIX
            function ensureAllInputsAreAccessible() {
                console.log('🔧 ENSURING ALL INPUTS ARE ACCESSIBLE...');

                // Get all input fields in the marks table
                const allInputs = document.querySelectorAll('.table-wrapper input[type="number"], .student-mark, .component-mark');
                console.log(`Found ${allInputs.length} input fields to fix`);

                allInputs.forEach((input, index) => {
                    // Remove any attributes that might disable the input
                    input.removeAttribute('disabled');
                    input.removeAttribute('readonly');
                    input.disabled = false;
                    input.readOnly = false;

                    // Ensure proper styling and accessibility
                    input.style.cssText += `
                        pointer-events: auto !important;
                        cursor: text !important;
                        opacity: 1 !important;
                        visibility: visible !important;
                        display: inline-block !important;
                        position: relative !important;
                        z-index: 1000 !important;
                        background-color: white !important;
                        border: 2px solid #007bff !important;
                        color: #333 !important;
                        font-weight: bold !important;
                    `;

                    // Add event listeners for better interaction
                    input.addEventListener('click', function(e) {
                        e.stopPropagation();
                        this.focus();
                        this.select();
                        console.log(`Input clicked: ${this.name || this.className}`);
                    });

                    input.addEventListener('focus', function() {
                        this.style.borderColor = '#28a745';
                        this.style.backgroundColor = '#f8fff8';
                        this.style.boxShadow = '0 0 5px rgba(40, 167, 69, 0.5)';
                        console.log(`Input focused: ${this.name || this.className}`);
                    });

                    input.addEventListener('blur', function() {
                        this.style.borderColor = '#007bff';
                        this.style.backgroundColor = 'white';
                        this.style.boxShadow = 'none';
                    });

                    input.addEventListener('input', function() {
                        console.log(`Input changed: ${this.name} = ${this.value}`);
                        // Trigger the existing percentage update function if it exists
                        if (typeof updatePercentage === 'function') {
                            updatePercentage(this);
                        }
                    });
                });

                // SPECIAL FIX FOR RIGHTMOST COLUMN (Creative Arts & Sports)
                const rightmostInputs = document.querySelectorAll('td:last-child input[type="number"]');
                console.log(`🎨 Found ${rightmostInputs.length} rightmost column inputs to fix`);

                rightmostInputs.forEach((input, index) => {
                    console.log(`🎨 Fixing rightmost input ${index + 1}: ${input.name}`);

                    // Force remove all restrictions
                    input.removeAttribute('disabled');
                    input.removeAttribute('readonly');
                    input.disabled = false;
                    input.readOnly = false;

                    // Clear any hardcoded values and reset to 0
                    if (input.value === '50') {
                        input.value = '0';
                        console.log(`🎨 Reset hardcoded value from 50 to 0 for input: ${input.name}`);
                    }

                    // Apply clean styling to ensure visibility and functionality
                    input.style.cssText = `
                        width: 100% !important;
                        min-width: 80px !important;
                        max-width: 120px !important;
                        height: 35px !important;
                        padding: 6px !important;
                        margin: 0 !important;
                        border: 1px solid #ced4da !important;
                        border-radius: 4px !important;
                        text-align: center !important;
                        background-color: white !important;
                        color: #333 !important;
                        font-size: 14px !important;
                        font-weight: normal !important;
                        pointer-events: auto !important;
                        cursor: text !important;
                        user-select: auto !important;
                        opacity: 1 !important;
                        z-index: 1000 !important;
                        position: relative !important;
                        display: block !important;
                        visibility: visible !important;
                        outline: none !important;
                        box-sizing: border-box !important;
                    `;

                    // Add specific event listeners for rightmost inputs
                    input.addEventListener('click', function(e) {
                        e.stopPropagation();
                        console.log(`🎨 Rightmost input clicked: ${this.name}`);
                        this.focus();
                        this.select();
                    });

                    input.addEventListener('focus', function() {
                        console.log(`🎨 Rightmost input focused: ${this.name}`);
                        this.style.borderColor = '#007bff';
                        this.style.backgroundColor = '#e3f2fd';
                        this.style.boxShadow = '0 0 5px rgba(0, 123, 255, 0.5)';
                    });

                    input.addEventListener('input', function() {
                        console.log(`🎨 Rightmost input changed: ${this.name} = ${this.value}`);
                        // Mark as user-modified so we don't reset it
                        this.setAttribute('data-user-modified', 'true');
                        // Trigger percentage update
                        if (typeof updatePercentage === 'function') {
                            updatePercentage(this);
                        }
                    });

                    input.addEventListener('keydown', function(e) {
                        console.log(`🎨 Key pressed in rightmost input: ${e.key}`);
                        // Allow all input
                        return true;
                    });

                    // Add click handler to the parent cell to redirect clicks to the input
                    const parentCell = input.closest('td');
                    if (parentCell) {
                        parentCell.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            console.log(`🎨 Cell clicked, focusing input: ${input.name}`);
                            input.focus();
                            input.select();
                        });

                        // Ensure the cell has proper styling
                        parentCell.style.cssText += `
                            cursor: text !important;
                            padding: 2px !important;
                        `;
                    }
                });

                console.log(`✅ Fixed ${allInputs.length} input fields for accessibility`);
            }

            // Debug function to analyze table structure
            function debugTableStructure() {
                console.log('🔍 DEBUGGING TABLE STRUCTURE...');

                const table = document.querySelector('.table-wrapper table');
                if (table) {
                    const headers = table.querySelectorAll('th');
                    const rows = table.querySelectorAll('tbody tr');

                    console.log(`📊 Table has ${headers.length} headers and ${rows.length} rows`);

                    headers.forEach((header, index) => {
                        const subjectId = header.getAttribute('data-subject-id');
                        console.log(`Header ${index + 1}: "${header.textContent.trim()}" (ID: ${subjectId})`);
                        if (index === headers.length - 1) {
                            console.log(`🎨 LAST HEADER: "${header.textContent.trim()}" (ID: ${subjectId})`);
                        }
                    });

                    if (rows.length > 0) {
                        const firstRow = rows[0];
                        const cells = firstRow.querySelectorAll('td');
                        console.log(`📊 First row has ${cells.length} cells`);

                        cells.forEach((cell, index) => {
                            const subjectId = cell.getAttribute('data-subject-id');
                            const input = cell.querySelector('input');
                            if (input) {
                                console.log(`Cell ${index + 1}: Subject ID="${subjectId}", Input name="${input.name}"`);
                                if (index === cells.length - 1) {
                                    console.log(`🎨 LAST CELL: Subject ID="${subjectId}", Input name="${input.name}"`);
                                }
                            }
                        });
                    }
                } else {
                    console.log('❌ No table found');
                }
            }

            // Test function to verify column mapping
            function testColumnMapping() {
                console.log('🧪 TESTING COLUMN MAPPING...');

                const checkboxes = document.querySelectorAll('.subject-checkbox');
                console.log(`Found ${checkboxes.length} subject checkboxes`);

                checkboxes.forEach((checkbox, index) => {
                    const subjectId = checkbox.getAttribute('data-subject-id');
                    const subjectName = checkbox.nextElementSibling?.textContent || 'Unknown';

                    // Find corresponding table elements
                    const headers = document.querySelectorAll(`th[data-subject-id="${subjectId}"]`);
                    const cells = document.querySelectorAll(`td[data-subject-id="${subjectId}"]`);

                    console.log(`Checkbox ${index + 1}: "${subjectName}" (ID: ${subjectId})`);
                    console.log(`  - Found ${headers.length} headers and ${cells.length} cells`);

                    if (headers.length === 0 || cells.length === 0) {
                        console.error(`❌ MAPPING ERROR: No table elements found for subject "${subjectName}" (ID: ${subjectId})`);
                    } else {
                        console.log(`✅ Mapping OK for "${subjectName}"`);
                    }
                });
            }

            // Function to populate all input fields with sample data
            function populateSampleData() {
                console.log('📝 POPULATING SAMPLE DATA...');

                const allInputs = document.querySelectorAll('.table-wrapper input[type="number"]');
                console.log(`Found ${allInputs.length} input fields to populate`);

                allInputs.forEach((input, index) => {
                    const subjectId = input.getAttribute('data-subject') || 'unknown';
                    const studentName = input.getAttribute('data-student') || 'unknown';
                    const maxMark = parseInt(input.getAttribute('max')) || 100;

                    // Generate sample mark (70-95% of max mark)
                    const sampleMark = Math.floor(maxMark * (0.7 + Math.random() * 0.25));

                    // Force set the value
                    input.value = sampleMark;
                    input.setAttribute('value', sampleMark);

                    // Trigger events to update percentages
                    input.dispatchEvent(new Event('input', { bubbles: true }));
                    input.dispatchEvent(new Event('change', { bubbles: true }));

                    console.log(`📝 Set ${studentName} - Subject ${subjectId}: ${sampleMark}/${maxMark}`);

                    // Special handling for Creative Arts (subject ID 19)
                    if (subjectId === '19') {
                        console.log(`🎨 CREATIVE ARTS INPUT: Set value ${sampleMark} for ${studentName}`);

                        // Extra aggressive setting for Creative Arts
                        setTimeout(() => {
                            input.value = sampleMark;
                            input.style.backgroundColor = '#90EE90'; // Light green to show it's populated
                        }, 100);
                    }
                });

                console.log(`✅ Populated ${allInputs.length} input fields with sample data`);
            }

            // COMPREHENSIVE RIGHTMOST COLUMN FIX
            function fixRightmostColumnsVisibility() {
                console.log('🔧 FIXING RIGHTMOST COLUMNS VISIBILITY...');

                // Fix table wrapper
                const tableWrapper = document.querySelector('.table-wrapper');
                if (tableWrapper) {
                    tableWrapper.style.cssText = `
                        overflow-x: auto !important;
                        overflow-y: visible !important;
                        position: relative !important;
                        width: 100% !important;
                        max-width: 100% !important;
                        display: block !important;
                        visibility: visible !important;
                        padding-right: 50px !important;
                    `;
                    console.log('✅ Table wrapper fixed');
                }

                // Fix table
                const table = document.querySelector('.table-wrapper table');
                if (table) {
                    table.style.cssText = `
                        width: 100% !important;
                        min-width: max-content !important;
                        table-layout: auto !important;
                        border-collapse: collapse !important;
                        display: table !important;
                        visibility: visible !important;
                    `;
                    console.log('✅ Table fixed');
                }

                // AGGRESSIVE FIX FOR CREATIVE ARTS COLUMN
                const allCells = document.querySelectorAll('.table-wrapper td, .table-wrapper th');
                console.log(`Found ${allCells.length} total cells`);

                // Find and fix the last column (Creative Arts)
                const lastColumnCells = document.querySelectorAll('.table-wrapper td:last-child, .table-wrapper th:last-child');
                console.log(`Found ${lastColumnCells.length} last column cells`);

                lastColumnCells.forEach((cell, index) => {
                    cell.style.cssText += `
                        background-color: #ffebcd !important;
                        border: 4px solid #ff0000 !important;
                        min-width: 150px !important;
                        width: 150px !important;
                        display: table-cell !important;
                        visibility: visible !important;
                        position: relative !important;
                        z-index: 1000 !important;
                        box-shadow: 0 0 15px rgba(255, 0, 0, 0.7) !important;
                    `;
                    console.log(`✅ Fixed last column cell ${index + 1}`);
                });

                // Fix rightmost cells specifically
                const rightmostCells = document.querySelectorAll(
                    'td:last-child, td:nth-last-child(2)'
                );
                rightmostCells.forEach((cell) => {
                    cell.style.cssText += `
                        background-color: #ffffff !important;
                        border: 1px solid #e9ecef !important;
                        min-width: 140px !important;
                        max-width: 140px !important;
                        width: 140px !important;
                        display: table-cell !important;
                        visibility: visible !important;
                        position: relative !important;
                        z-index: auto !important;
                        overflow: visible !important;
                    `;
                });

                // AGGRESSIVE FIX FOR CREATIVE ARTS INPUT FIELDS (Subject ID: 19)
                const creativeArtsInputs = document.querySelectorAll('td[data-subject-id="19"] input, td:last-child input');
                console.log(`Found ${creativeArtsInputs.length} Creative Arts input fields`);

                creativeArtsInputs.forEach((input, index) => {
                    // Force remove any restrictions
                    input.removeAttribute('disabled');
                    input.removeAttribute('readonly');
                    input.removeAttribute('tabindex');
                    input.style.pointerEvents = 'auto';
                    input.style.userSelect = 'auto';

                    // Apply aggressive styling
                    input.style.cssText = `
                        width: 130px !important;
                        height: 40px !important;
                        display: block !important;
                        visibility: visible !important;
                        background-color: #ffffff !important;
                        border: 3px solid #ff0000 !important;
                        border-radius: 6px !important;
                        padding: 10px !important;
                        font-size: 16px !important;
                        font-weight: bold !important;
                        z-index: 1001 !important;
                        position: relative !important;
                        pointer-events: auto !important;
                        opacity: 1 !important;
                        box-shadow: 0 0 10px rgba(255, 0, 0, 0.5) !important;
                        margin: 2px !important;
                        user-select: auto !important;
                        -webkit-user-select: auto !important;
                        -moz-user-select: auto !important;
                        -ms-user-select: auto !important;
                    `;

                    // Force enable the input
                    input.disabled = false;
                    input.readOnly = false;

                    // Add comprehensive event listeners
                    input.addEventListener('focus', function() {
                        console.log(`🎯 Creative Arts input ${index + 1} focused:`, this.name);
                        this.style.backgroundColor = '#e3f2fd';
                        this.style.border = '4px solid #2196f3';
                        this.disabled = false;
                        this.readOnly = false;
                    });

                    input.addEventListener('blur', function() {
                        console.log(`🎯 Creative Arts input ${index + 1} blurred:`, this.name, 'Value:', this.value);
                        this.style.backgroundColor = '#ffffff';
                        this.style.border = '3px solid #ff0000';
                    });

                    input.addEventListener('input', function() {
                        console.log(`🎯 Creative Arts input ${index + 1} changed:`, this.value);
                    });

                    input.addEventListener('keydown', function(e) {
                        console.log(`🎯 Creative Arts input ${index + 1} keydown:`, e.key);
                        // Allow all number keys, backspace, delete, tab, enter, arrows
                        if (e.key === 'Tab' || e.key === 'Enter' || e.key === 'Backspace' || e.key === 'Delete' ||
                            e.key === 'ArrowLeft' || e.key === 'ArrowRight' || e.key === 'ArrowUp' || e.key === 'ArrowDown' ||
                            (e.key >= '0' && e.key <= '9')) {
                            return true;
                        }
                        return true; // Allow all keys for testing
                    });

                    input.addEventListener('click', function() {
                        console.log(`🎯 Creative Arts input ${index + 1} clicked`);
                        this.focus();
                        this.select(); // Select all text for easy editing
                    });

                    // Test if we can set a value programmatically
                    const testValue = '50';
                    input.value = testValue;
                    console.log(`✅ Fixed Creative Arts input ${index + 1}: ${input.name}, Test value set: ${input.value}`);
                });

                // Fix all other rightmost inputs
                const otherRightmostInputs = document.querySelectorAll(
                    'td:nth-last-child(2) input, td:nth-last-child(3) input'
                );
                otherRightmostInputs.forEach((input) => {
                    input.style.cssText += `
                        width: 100% !important;
                        max-width: 120px !important;
                        min-width: 80px !important;
                        display: block !important;
                        visibility: visible !important;
                        background-color: #fff !important;
                        border: 2px solid #007bff !important;
                        border-radius: 4px !important;
                        padding: 8px !important;
                        font-size: 14px !important;
                        z-index: 101 !important;
                        position: relative !important;
                        pointer-events: auto !important;
                    `;
                });

                console.log(`✅ Fixed ${rightmostCells.length} rightmost cells and ${rightmostInputs.length} inputs`);
            }

            // Function to scroll to rightmost columns
            function scrollToRightmostColumns() {
                const tableWrapper = document.querySelector('.table-wrapper');
                if (tableWrapper) {
                    // Force scroll to the absolute right
                    tableWrapper.scrollLeft = tableWrapper.scrollWidth - tableWrapper.clientWidth;
                    console.log('📜 Scrolled to rightmost columns');

                    // Also try to focus on the first Creative Arts input
                    setTimeout(() => {
                        const creativeArtsInput = document.querySelector('td:last-child input');
                        if (creativeArtsInput) {
                            creativeArtsInput.focus();
                            creativeArtsInput.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'end' });
                            console.log('🎯 Focused on Creative Arts input');
                        }
                    }, 500);
                }
            }

            // Function specifically for Creative Arts column
            function focusCreativeArtsColumn() {
                console.log('🎨 FOCUSING ON CREATIVE ARTS COLUMN...');

                const tableWrapper = document.querySelector('.table-wrapper');
                if (tableWrapper) {
                    // Scroll to the rightmost position
                    tableWrapper.scrollLeft = tableWrapper.scrollWidth;
                }

                // Find and highlight all Creative Arts inputs
                const creativeArtsInputs = document.querySelectorAll('td[data-subject-id="19"] input, td:last-child input');
                creativeArtsInputs.forEach((input, index) => {
                    // Force enable the input
                    input.disabled = false;
                    input.readOnly = false;
                    input.removeAttribute('disabled');
                    input.removeAttribute('readonly');

                    input.style.cssText += `
                        animation: pulse 2s infinite !important;
                        background-color: #ffff00 !important;
                        border: 5px solid #ff0000 !important;
                        pointer-events: auto !important;
                    `;

                    if (index === 0) {
                        input.focus();
                        input.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'end' });

                        // Try to set a test value
                        setTimeout(() => {
                            input.value = '75';
                            input.dispatchEvent(new Event('input', { bubbles: true }));
                            console.log(`🎨 Set test value 75 in first Creative Arts input: ${input.value}`);
                        }, 500);
                    }
                });

                console.log(`🎨 Highlighted ${creativeArtsInputs.length} Creative Arts inputs`);
            }

            // Function to force test Creative Arts input
            function forceTestCreativeArts() {
                console.log('🔧 FORCE TESTING CREATIVE ARTS INPUTS...');

                const creativeArtsInputs = document.querySelectorAll('td[data-subject-id="19"] input, td:last-child input');
                console.log(`Found ${creativeArtsInputs.length} Creative Arts inputs to test`);

                creativeArtsInputs.forEach((input, index) => {
                    console.log(`Testing input ${index + 1}:`);
                    console.log(`  - Name: ${input.name}`);
                    console.log(`  - Disabled: ${input.disabled}`);
                    console.log(`  - ReadOnly: ${input.readOnly}`);
                    console.log(`  - Current value: ${input.value}`);
                    console.log(`  - Max: ${input.max}`);

                    // Force enable
                    input.disabled = false;
                    input.readOnly = false;

                    // Try to set value
                    const testValue = 80 + index;
                    input.value = testValue;

                    console.log(`  - After setting ${testValue}: ${input.value}`);

                    // Test if we can type
                    input.focus();
                    input.select();

                    // Simulate typing
                    const event = new KeyboardEvent('keydown', { key: '9' });
                    input.dispatchEvent(event);

                    console.log(`  - After simulated typing: ${input.value}`);
                });
            }

            // SIMPLE RIGHTMOST COLUMN FIX FUNCTION
            function fixRightmostColumnInputs() {
                console.log('🎨 SIMPLE FIX FOR RIGHTMOST COLUMN INPUTS...');

                // Target the rightmost column inputs specifically
                const rightmostInputs = document.querySelectorAll('td:last-child input[type="number"]');
                console.log(`Found ${rightmostInputs.length} rightmost inputs`);

                rightmostInputs.forEach((input, index) => {
                    // Reset hardcoded values
                    if (input.value === '50') {
                        input.value = '0';
                    }

                    // Ensure the input is fully functional
                    input.disabled = false;
                    input.readOnly = false;
                    input.removeAttribute('disabled');
                    input.removeAttribute('readonly');

                    // Get the parent cell
                    const parentCell = input.closest('td');

                    // Simple clean styling with proper clickable area
                    input.style.cssText = `
                        width: 100% !important;
                        min-width: 80px !important;
                        height: 35px !important;
                        border: 1px solid #ced4da !important;
                        background-color: white !important;
                        pointer-events: auto !important;
                        cursor: text !important;
                        display: block !important;
                        padding: 6px !important;
                        margin: 0 !important;
                        text-align: center !important;
                        border-radius: 4px !important;
                        box-sizing: border-box !important;
                        position: relative !important;
                        z-index: 1000 !important;
                        font-size: 14px !important;
                        font-weight: normal !important;
                    `;

                    // Make the entire cell clickable
                    if (parentCell) {
                        parentCell.style.cssText += `
                            cursor: text !important;
                            padding: 6px !important;
                            text-align: center !important;
                            background-color: #f8f9fa !important;
                        `;

                        // Remove existing click handlers and add new one
                        parentCell.onclick = function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            input.focus();
                            input.select();
                        };
                    }

                    console.log(`Fixed rightmost input ${index + 1}: ${input.name}`);
                });
            }

            // Apply fixes immediately and repeatedly
            ensureAllInputsAreAccessible();
            fixRightmostColumnInputs();
            fixRightmostColumnsVisibility();
            setTimeout(() => {
                ensureAllInputsAreAccessible();
                fixRightmostColumnInputs();
                fixRightmostColumnsVisibility();
                debugTableStructure();
                testColumnMapping();
            }, 500);
            setTimeout(() => {
                ensureAllInputsAreAccessible();
                fixRightmostColumnInputs();
                fixRightmostColumnsVisibility();
                debugTableStructure();
                testColumnMapping();
            }, 1500);
            setTimeout(() => {
                ensureAllInputsAreAccessible();
                fixRightmostColumnInputs();
                fixRightmostColumnsVisibility();
                debugTableStructure();
                testColumnMapping();
            }, 3000);

            // Set up periodic checks for rightmost column inputs
            setInterval(() => {
                const rightmostInputs = document.querySelectorAll('td:last-child input[type="number"]');
                if (rightmostInputs.length > 0) {
                    rightmostInputs.forEach(input => {
                        // Reset hardcoded values
                        if (input.value === '50' && !input.hasAttribute('data-user-modified')) {
                            input.value = '0';
                            console.log('🔧 Reset hardcoded value 50 to 0');
                        }

                        // Ensure the input is always accessible
                        if (input.disabled || input.readOnly ||
                            input.style.display === 'none' ||
                            input.style.visibility === 'hidden' ||
                            input.style.pointerEvents === 'none') {

                            console.log('🔧 Re-fixing inaccessible rightmost input');
                            input.disabled = false;
                            input.readOnly = false;
                            input.removeAttribute('disabled');
                            input.removeAttribute('readonly');

                            input.style.cssText = `
                                width: 100% !important;
                                min-width: 80px !important;
                                max-width: 120px !important;
                                height: 35px !important;
                                display: block !important;
                                visibility: visible !important;
                                pointer-events: auto !important;
                                cursor: text !important;
                                background-color: white !important;
                                border: 1px solid #ced4da !important;
                                z-index: 1000 !important;
                                position: relative !important;
                                color: #333 !important;
                                font-weight: normal !important;
                                padding: 6px !important;
                                margin: 0 !important;
                                text-align: center !important;
                                border-radius: 4px !important;
                                box-sizing: border-box !important;
                            `;
                        }
                    });
                }
            }, 1000); // Check every 1 second

            // Add scroll to rightmost columns button functionality
            const uploadSection = document.getElementById('upload-marks-section');
            if (uploadSection) {
                // Create scroll button
                const scrollButton = document.createElement('button');
                scrollButton.type = 'button';
                scrollButton.className = 'modern-btn btn-outline';
                scrollButton.style.cssText = `
                    margin: 5px;
                    background-color: #3498db;
                    color: white;
                    border: none;
                    padding: 10px 15px;
                    border-radius: 5px;
                    cursor: pointer;
                `;
                scrollButton.innerHTML = '➡️ Show Rightmost Columns';
                scrollButton.onclick = scrollToRightmostColumns;

                // Create Creative Arts focus button
                const creativeArtsButton = document.createElement('button');
                creativeArtsButton.type = 'button';
                creativeArtsButton.className = 'modern-btn btn-outline';
                creativeArtsButton.style.cssText = `
                    margin: 5px;
                    background-color: #ff0000;
                    color: white;
                    border: none;
                    padding: 10px 15px;
                    border-radius: 5px;
                    cursor: pointer;
                    font-weight: bold;
                `;
                creativeArtsButton.innerHTML = '🎨 Focus Creative Arts';
                creativeArtsButton.onclick = focusCreativeArtsColumn;

                // Create test mapping button
                const testMappingButton = document.createElement('button');
                testMappingButton.type = 'button';
                testMappingButton.className = 'modern-btn btn-outline';
                testMappingButton.style.cssText = `
                    margin: 5px;
                    background-color: #007bff;
                    color: white;
                    border: none;
                    padding: 10px 15px;
                    border-radius: 5px;
                    cursor: pointer;
                `;
                testMappingButton.innerHTML = '🧪 Test Column Mapping';
                testMappingButton.onclick = testColumnMapping;

                // Create sample data button
                const sampleDataButton = document.createElement('button');
                sampleDataButton.type = 'button';
                sampleDataButton.className = 'modern-btn btn-outline';
                sampleDataButton.style.cssText = `
                    margin: 5px;
                    background-color: #28a745;
                    color: white;
                    border: none;
                    padding: 10px 15px;
                    border-radius: 5px;
                    cursor: pointer;
                `;
                sampleDataButton.innerHTML = '📝 Fill Sample Data';
                sampleDataButton.onclick = populateSampleData;

                // Create force test button
                const forceTestButton = document.createElement('button');
                forceTestButton.type = 'button';
                forceTestButton.className = 'modern-btn btn-outline';
                forceTestButton.style.cssText = `
                    margin: 5px;
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    padding: 10px 15px;
                    border-radius: 5px;
                    cursor: pointer;
                `;
                forceTestButton.innerHTML = '🔧 Force Test Creative Arts';
                forceTestButton.onclick = forceTestCreativeArts;

                // Create simple rightmost fix button
                const simpleFixButton = document.createElement('button');
                simpleFixButton.type = 'button';
                simpleFixButton.className = 'modern-btn btn-outline';
                simpleFixButton.style.cssText = `
                    margin: 5px;
                    background-color: #6f42c1;
                    color: white;
                    border: none;
                    padding: 10px 15px;
                    border-radius: 5px;
                    cursor: pointer;
                    font-weight: bold;
                `;
                simpleFixButton.innerHTML = '🎯 Fix Input Fields';
                simpleFixButton.onclick = fixRightmostColumnInputs;

                // Insert the buttons after the form header
                const cardHeader = uploadSection.querySelector('.card-header');
                if (cardHeader) {
                    cardHeader.appendChild(scrollButton);
                    cardHeader.appendChild(creativeArtsButton);
                    cardHeader.appendChild(testMappingButton);
                    cardHeader.appendChild(sampleDataButton);
                    cardHeader.appendChild(forceTestButton);
                    cardHeader.appendChild(simpleFixButton);
                }
            }

            // Re-apply fixes when marks form is loaded
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        const addedNodes = Array.from(mutation.addedNodes);
                        const hasTable = addedNodes.some(node =>
                            node.nodeType === 1 &&
                            (node.classList?.contains('table-wrapper') || node.querySelector?.('.table-wrapper'))
                        );

                        if (hasTable) {
                            console.log('🔄 Table detected, re-applying fixes...');
                            setTimeout(() => {
                                ensureAllInputsAreAccessible();
                                fixRightmostColumnInputs();
                                fixRightmostColumnsVisibility();
                                debugTableStructure();
                            }, 100);
                        }
                    }
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            console.log('✅ RIGHTMOST COLUMN FIXES APPLIED SUCCESSFULLY');
        });

        // Mobile Navigation Toggle for Classteacher
        function toggleClassteacherNav() {
            const classteacherNav = document.getElementById('classteacherNav');
            const toggleBtn = document.querySelector('.mobile-nav-toggle i');

            if (classteacherNav.classList.contains('show')) {
                classteacherNav.classList.remove('show');
                toggleBtn.className = 'fas fa-bars';
            } else {
                classteacherNav.classList.add('show');
                toggleBtn.className = 'fas fa-times';
            }
        }

        // Close mobile nav when clicking outside
        document.addEventListener('click', function(event) {
            const navSystem = document.querySelector('.enhanced-nav-system');
            const classteacherNav = document.getElementById('classteacherNav');
            const toggleBtn = document.querySelector('.mobile-nav-toggle');

            if (!navSystem.contains(event.target) && classteacherNav.classList.contains('show')) {
                classteacherNav.classList.remove('show');
                toggleBtn.querySelector('i').className = 'fas fa-bars';
            }
        });

        // Handle window resize for classteacher nav
        window.addEventListener('resize', function() {
            const classteacherNav = document.getElementById('classteacherNav');
            const toggleBtn = document.querySelector('.mobile-nav-toggle i');

            if (window.innerWidth > 768 && classteacherNav.classList.contains('show')) {
                classteacherNav.classList.remove('show');
                toggleBtn.className = 'fas fa-bars';
            }
        });

        // Show/hide mobile nav toggle based on screen size
        function updateMobileNavToggle() {
            const toggleBtn = document.querySelector('.mobile-nav-toggle');
            if (toggleBtn) {
                if (window.innerWidth <= 768) {
                    toggleBtn.style.display = 'block';
                } else {
                    toggleBtn.style.display = 'none';
                    // Ensure nav is visible on larger screens
                    const classteacherNav = document.getElementById('classteacherNav');
                    if (classteacherNav) {
                        classteacherNav.classList.remove('show');
                    }
                }
            }
        }

        // Initialize mobile nav toggle visibility
        updateMobileNavToggle();
        window.addEventListener('resize', updateMobileNavToggle);

        // Function to download all individual reports as ZIP
        async function downloadAllIndividualReports(grade, stream, term, assessmentType) {
            const downloadBtn = document.getElementById('downloadAllBtn');
            const originalText = downloadBtn.innerHTML;

            try {
                // Show loading state
                downloadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating ZIP...';
                downloadBtn.disabled = true;

                // Make the request
                const response = await fetch(`/classteacher/generate_all_individual_reports/${encodeURIComponent(grade)}/${encodeURIComponent(stream)}/${encodeURIComponent(term)}/${encodeURIComponent(assessmentType)}`, {
                    method: 'GET',
                    credentials: 'same-origin'
                });

                if (!response.ok) {
                    // If response is not ok, try to get error message
                    const errorText = await response.text();
                    throw new Error(`Server error: ${response.status} - ${errorText}`);
                }

                // Check if response is actually a ZIP file
                const contentType = response.headers.get('Content-Type');
                if (!contentType || !contentType.includes('application/zip')) {
                    // This might be a redirect response (HTML), show appropriate message
                    throw new Error('No reports could be generated. Please ensure students have marks for the selected term and assessment type.');
                }

                // Get filename from response headers
                const contentDisposition = response.headers.get('Content-Disposition');
                let filename = `Individual_Reports_${grade.replace(' ', '_')}_${stream}_${term}_${assessmentType}_${new Date().toISOString().slice(0, 10)}.zip`;

                if (contentDisposition) {
                    const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                    if (filenameMatch) {
                        filename = filenameMatch[1];
                    }
                }

                // Download the file
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                // Show success message
                showNotification('ZIP file downloaded successfully!', 'success');

            } catch (error) {
                console.error('Download error:', error);
                showNotification(error.message || 'Error downloading ZIP file. Please try again.', 'error');
            } finally {
                // Restore button state
                downloadBtn.innerHTML = originalText;
                downloadBtn.disabled = false;
            }
        }

        // Notification function (if not already defined)
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
                color: white;
                padding: 15px 20px;
                border-radius: 5px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                z-index: 10000;
                max-width: 400px;
                word-wrap: break-word;
            `;
            notification.textContent = message;

            // Add close button
            const closeBtn = document.createElement('button');
            closeBtn.innerHTML = '×';
            closeBtn.style.cssText = `
                background: none;
                border: none;
                color: white;
                font-size: 20px;
                font-weight: bold;
                margin-left: 10px;
                cursor: pointer;
                padding: 0;
                line-height: 1;
            `;
            closeBtn.onclick = () => notification.remove();
            notification.appendChild(closeBtn);

            // Add to page
            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    </script>

</body>
</html>