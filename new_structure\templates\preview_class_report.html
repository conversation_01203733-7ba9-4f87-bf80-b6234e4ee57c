<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Class Report</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/style.css') }}"
    />
    <style>
      body {
        font-family: "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
        margin: 0;
        padding: 15px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #333;
        font-size: 12px;
        min-height: 100vh;
      }
      .report-container {
        max-width: 100%;
        margin: 0 auto;
        background: white;
        padding: 0;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        border-radius: 12px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
      }
      .header {
        background: linear-gradient(
          135deg,
          #1e3c72 0%,
          #2a5298 50%,
          #4a90e2 100%
        );
        color: white;
        padding: 20px 30px;
        margin: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: bold;
        position: relative;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      }

      .header::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          45deg,
          rgba(255, 255, 255, 0.1) 0%,
          transparent 100%
        );
        pointer-events: none;
      }

      .header-left {
        display: flex;
        align-items: center;
        gap: 15px;
        z-index: 1;
      }

      .header-logo {
        max-width: 80px;
        max-height: 80px;
        min-width: 40px;
        min-height: 40px;
        width: auto;
        height: auto;
        border-radius: 8px;
        border: 2px solid rgba(255, 255, 255, 0.4);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        object-fit: contain;
        background: rgba(255, 255, 255, 0.9);
        padding: 4px;
        transition: all 0.3s ease;
      }

      .header-logo.circular {
        border-radius: 50%;
      }

      .header-logo.square {
        border-radius: 8px;
      }

      .header-logo.rounded {
        border-radius: 12px;
      }

      /* Adaptive logo sizing based on content */
      .header-logo.small {
        max-width: 50px;
        max-height: 50px;
      }

      .header-logo.medium {
        max-width: 70px;
        max-height: 70px;
      }

      .header-logo.large {
        max-width: 90px;
        max-height: 90px;
      }

      /* Logo container for better positioning */
      .logo-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 60px;
        padding: 5px;
        position: relative;
      }

      /* Fallback for missing logos */
      .logo-fallback {
        width: 60px;
        height: 60px;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.9),
          rgba(255, 255, 255, 0.7)
        );
        border: 2px solid rgba(255, 255, 255, 0.4);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        font-weight: bold;
        color: #1e3c72;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      /* Color adaptation for different logo backgrounds */
      .header-logo.dark-bg {
        background: rgba(255, 255, 255, 0.95);
        border-color: rgba(255, 255, 255, 0.6);
      }

      .header-logo.light-bg {
        background: rgba(0, 0, 0, 0.05);
        border-color: rgba(0, 0, 0, 0.1);
      }

      .header-logo.transparent-bg {
        background: transparent;
        border-color: rgba(255, 255, 255, 0.3);
      }

      /* Enhanced print logo styling */
      @media print {
        .header-logo {
          -webkit-print-color-adjust: exact !important;
          color-adjust: exact !important;
          max-width: 70px !important;
          max-height: 70px !important;
        }

        .logo-wrapper {
          min-width: 50px !important;
        }

        .logo-fallback {
          -webkit-print-color-adjust: exact !important;
          color-adjust: exact !important;
        }
      }

      .header-right {
        text-align: right;
        font-size: 20px;
        font-weight: 700;
        letter-spacing: 2px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        z-index: 1;
      }

      .school-info {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      }

      .school-name {
        font-size: 18px;
        font-weight: 700;
        margin-bottom: 4px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .school-details {
        font-size: 11px;
        opacity: 0.95;
        font-weight: 400;
        line-height: 1.4;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
      }

      .class-info-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 15px 30px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 13px;
        font-weight: 600;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
      }

      .class-info-left {
        display: flex;
        gap: 40px;
      }

      .class-info-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 15px;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.5);
      }

      .class-info-label {
        color: #495057;
        font-weight: 500;
        font-size: 11px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .class-info-value {
        color: #1e3c72;
        font-weight: 700;
        font-size: 13px;
      }

      .logo-container {
        margin-bottom: 10px;
      }

      .school-logo {
        max-width: 200px;
        height: auto;
      }

      .school-info {
        text-align: center;
      }

      .header h1 {
        margin: 0;
        font-size: 32px;
        color: #2c3e50;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 2px;
        position: relative;
        display: inline-block;
      }

      .header h1:before,
      .header h1:after {
        content: "★";
        color: #3498db;
        font-size: 18px;
        position: relative;
        top: -5px;
        margin: 0 10px;
      }

      .header p {
        margin: 8px 0;
        font-size: 16px;
        color: #34495e;
      }
      .report-title {
        font-size: 24px;
        font-weight: bold;
        color: #2c3e50;
        margin: 15px 0 10px;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        letter-spacing: 1px;
      }

      .report-subtitle {
        font-size: 16px;
        color: #34495e;
        margin: 5px 0 15px;
        background-color: #f8f9fa;
        padding: 8px 15px;
        border-radius: 5px;
        display: inline-block;
      }

      .report-detail {
        font-weight: 600;
        padding: 0 5px;
      }
      table {
        width: 100%;
        border-collapse: collapse;
        margin: 0;
        font-size: 11px;
        background: white;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border-radius: 0 0 12px 12px;
        overflow: hidden;
      }
      th,
      td {
        border: 1px solid rgba(0, 0, 0, 0.08);
        padding: 8px 6px;
        text-align: center;
        font-size: 10px;
      }
      th {
        background: linear-gradient(135deg, #495057 0%, #6c757d 100%);
        color: white;
        font-weight: 700;
        font-size: 9px;
        text-transform: uppercase;
        letter-spacing: 0.8px;
        padding: 12px 6px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        position: relative;
      }

      th::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, #1e3c72, #2a5298, #4a90e2);
      }

      .subject-header {
        writing-mode: vertical-rl;
        text-orientation: mixed;
        width: 25px;
        min-width: 25px;
        max-width: 25px;
      }

      .composite-main-header {
        background-color: #e3f2fd !important;
        color: #0d47a1 !important;
        font-weight: bold;
        writing-mode: horizontal-tb !important;
        text-orientation: mixed;
        text-align: center;
        border-bottom: 2px solid #1976d2;
      }

      .component-header {
        background-color: #f5f5f5;
        color: #333;
        font-size: 10px;
        font-weight: normal;
        writing-mode: vertical-rl;
        text-orientation: mixed;
        width: 25px;
        min-width: 25px;
        max-width: 25px;
        border-top: 1px solid #ddd;
      }

      .performance-summary {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 20px 30px;
        margin-top: 0;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
      }

      .performance-summary h3 {
        margin: 0 0 15px 0;
        font-size: 16px;
        font-weight: 700;
        color: #1e3c72;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .performance-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 15px;
        font-size: 12px;
      }

      .performance-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 15px 12px;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.5);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .performance-item::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
      }

      .performance-item.exceeding {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
      }

      .performance-item.exceeding::before {
        background: linear-gradient(90deg, #28a745, #20c997);
      }

      .performance-item.meeting {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
      }

      .performance-item.meeting::before {
        background: linear-gradient(90deg, #ffc107, #fd7e14);
      }

      .performance-item.approaching {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
      }

      .performance-item.approaching::before {
        background: linear-gradient(90deg, #dc3545, #e74c3c);
      }

      .performance-item.below {
        background: linear-gradient(135deg, #f5c6cb 0%, #f1b0b7 100%);
      }

      .performance-item.below::before {
        background: linear-gradient(90deg, #721c24, #a71e34);
      }

      .performance-item span {
        font-size: 10px;
        color: #495057;
        text-align: center;
        margin-bottom: 8px;
        font-weight: 500;
      }

      .performance-item strong {
        font-size: 14px;
        font-weight: 700;
        color: #2c3e50;
      }

      .signature-section {
        display: flex;
        justify-content: space-between;
        margin-top: 40px;
        padding: 30px;
        font-size: 12px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-top: 1px solid rgba(0, 0, 0, 0.1);
      }

      .signature-item {
        text-align: center;
        flex: 1;
        padding: 20px;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 12px;
        margin: 0 10px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.5);
      }

      .signature-item strong {
        color: #1e3c72;
        font-size: 13px;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 15px;
        display: block;
      }

      .signature-line {
        border-bottom: 2px solid #1e3c72;
        width: 180px;
        margin: 25px auto 10px;
        position: relative;
      }

      .signature-line::after {
        content: "✓";
        position: absolute;
        right: -20px;
        top: -15px;
        color: #28a745;
        font-weight: bold;
        font-size: 16px;
      }
      tbody tr:nth-child(even) {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      }

      tbody tr:hover {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        transform: translateY(-1px);
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .student-row td:first-child {
        font-weight: 700;
        text-align: center;
        width: 40px;
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
        font-size: 11px;
      }

      .student-row td:nth-child(2) {
        text-align: left;
        padding-left: 12px;
        font-weight: 600;
        min-width: 180px;
        font-size: 11px;
        color: #2c3e50;
      }
      .student-name {
        text-align: left;
        font-weight: 500;
      }
      .total-cell {
        font-weight: bold;
        background-color: #eaf2f8;
      }
      .avg-cell {
        font-weight: bold;
        color: #2980b9;
      }
      .grade-cell {
        font-weight: bold;
      }
      .rank-cell {
        font-weight: bold;
        background-color: #f5f5f5;
      }
      .averages-row {
        background-color: #eaf2f8;
        font-weight: bold;
      }
      .average-label {
        text-align: center;
        background-color: #3498db;
        color: white;
      }
      .subject-average,
      .total-average {
        background-color: #d6eaf8;
        color: #2980b9;
      }
      .class-average-row {
        background-color: #d4efdf;
      }
      .class-average-row .average-label {
        background-color: #27ae60;
      }
      .empty-cell {
        background-color: #f9f9f9;
      }
      .stats {
        margin-top: 40px;
        padding: 25px;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.9) 0%,
          rgba(248, 249, 250, 0.9) 100%
        );
        backdrop-filter: blur(10px);
        border-radius: 15px;
        border: 1px solid rgba(52, 152, 219, 0.2);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
      }
      .stats h3 {
        margin-top: 0;
        color: #2c3e50;
        font-size: 18px;
        border-bottom: 1px solid #ddd;
        padding-bottom: 10px;
      }
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        margin-top: 15px;
      }
      .stat-item {
        padding: 10px;
        border-radius: 5px;
      }
      .exceeding {
        background-color: #d4edda;
        color: #155724;
      }
      .meeting {
        background-color: #cce5ff;
        color: #004085;
      }
      .approaching {
        background-color: #fff3cd;
        color: #856404;
      }
      .below {
        background-color: #f8d7da;
        color: #721c24;
      }
      .footer {
        margin-top: 40px;
        text-align: center;
        font-size: 0.9em;
        color: #7f8c8d;
        border-top: 2px solid transparent;
        border-image: linear-gradient(
            90deg,
            transparent,
            #3498db,
            #9b59b6,
            #3498db,
            transparent
          )
          1;
        padding-top: 25px;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.5) 0%,
          rgba(248, 249, 250, 0.5) 100%
        );
        border-radius: 10px;
        margin-top: 30px;
        padding: 20px;
      }
      /* Subject Teachers Section */
      .subject-teachers-section {
        margin: 30px 0;
        padding: 20px;
        background-color: #f9f9f9;
        border-radius: 8px;
        border: 1px solid #ddd;
        page-break-inside: avoid;
      }

      .subject-teachers-section h3 {
        margin-bottom: 15px;
        color: #333;
        font-size: 16px;
        text-align: center;
        border-bottom: 2px solid #1f7d53;
        padding-bottom: 8px;
      }

      .teachers-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
        margin-top: 15px;
      }

      .teacher-info {
        background: white;
        padding: 12px;
        border-radius: 6px;
        border-left: 4px solid #1f7d53;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .teacher-info strong {
        color: #1f7d53;
        font-size: 14px;
      }

      .teacher-info small {
        color: #666;
        font-style: italic;
      }

      .signature-section {
        display: flex;
        justify-content: space-between;
        margin-top: 50px;
        margin-bottom: 30px;
      }
      .signature-box {
        width: 30%;
        text-align: center;
      }
      .signature-line {
        border-bottom: 1px solid #333;
        margin-bottom: 5px;
        height: 40px;
      }

      .teacher-name {
        font-weight: bold;
        color: #1f7d53;
        font-size: 12px;
        margin-top: 5px;
      }
      .action-buttons {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
      }
      .back-btn,
      .edit-btn,
      .delete-btn {
        display: inline-block;
        padding: 10px 20px;
        color: white;
        text-decoration: none;
        border-radius: 5px;
        transition: background-color 0.3s;
        font-weight: 500;
        border: none;
        cursor: pointer;
      }
      .back-btn {
        background-color: #3498db;
      }
      .back-btn:hover {
        background-color: #2980b9;
      }
      .edit-btn {
        background-color: #27ae60;
      }
      .edit-btn:hover {
        background-color: #219653;
      }
      .delete-btn {
        background-color: #e74c3c;
      }
      .delete-btn:hover {
        background-color: #c0392b;
      }

      /* Modal styles */
      .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
      }
      .modal-content {
        background-color: white;
        margin: 15% auto;
        padding: 20px;
        border-radius: 8px;
        width: 50%;
        max-width: 500px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }
      .modal-buttons {
        display: flex;
        justify-content: flex-end;
        margin-top: 20px;
        gap: 10px;
      }
      .cancel-btn {
        background-color: #95a5a6;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
      }
      .cancel-btn:hover {
        background-color: #7f8c8d;
      }
      .confirm-delete-btn {
        background-color: #e74c3c;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
      }
      .confirm-delete-btn:hover {
        background-color: #c0392b;
      }

      /* Print Controls Styles */
      .print-controls {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 20px;
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 5px solid #3498db;
        align-items: center;
      }

      .print-btn,
      .instructions-toggle {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 10px 15px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-weight: 500;
        font-size: 14px;
        transition: all 0.3s ease;
        text-decoration: none;
        color: white;
      }

      .print-btn {
        background-color: #3498db;
      }

      .print-btn:hover {
        background-color: #2980b9;
      }

      .instructions-toggle {
        background-color: #f39c12;
        margin-left: auto;
      }

      .instructions-toggle:hover {
        background-color: #d35400;
      }

      .print-icon,
      .info-icon {
        font-style: normal;
      }

      .instructions-panel {
        position: absolute;
        background-color: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 15px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        z-index: 100;
        width: 350px;
        right: 20px;
        margin-top: 10px;
      }

      .instructions-panel h4 {
        margin-top: 0;
        color: #2c3e50;
        border-bottom: 1px solid #eee;
        padding-bottom: 8px;
      }

      .instructions-panel ol {
        margin: 0;
        padding-left: 20px;
      }

      .instructions-panel li {
        margin-bottom: 8px;
      }

      .print-instructions {
        position: relative;
      }

      @media print {
        /* Remove all browser-generated content */
        @page {
          size: landscape;
          margin: 0.8cm 0.5cm;
          /* Remove headers and footers completely */
          @top-left {
            content: "";
          }
          @top-center {
            content: "";
          }
          @top-right {
            content: "";
          }
          @bottom-left {
            content: "";
          }
          @bottom-center {
            content: "";
          }
          @bottom-right {
            content: "";
          }
        }

        /* Reset all styles for clean printing */
        * {
          -webkit-print-color-adjust: exact !important;
          color-adjust: exact !important;
          print-color-adjust: exact !important;
        }

        html {
          margin: 0 !important;
          padding: 0 !important;
        }

        body {
          background: white !important;
          padding: 0 !important;
          margin: 0 !important;
          font-size: 11pt !important;
          font-family: "Poppins", Arial, sans-serif !important;
          -webkit-print-color-adjust: exact !important;
          color-adjust: exact !important;
        }

        .report-container {
          box-shadow: none !important;
          padding: 15px !important;
          max-width: 100% !important;
          margin: 0 !important;
          background: white !important;
          border-radius: 0 !important;
          backdrop-filter: none !important;
          border: none !important;
        }

        /* Hide all non-essential elements */
        .action-buttons,
        .print-controls,
        .delete-btn,
        .modal,
        .subject-selection-form,
        .instructions-panel,
        .print-instructions {
          display: none !important;
          visibility: hidden !important;
        }

        .header {
          margin-bottom: 20px !important;
          page-break-inside: avoid !important;
          background: white !important;
          border: 1px solid #ddd !important;
          padding: 15px !important;
        }

        .school-logo {
          max-width: 120px !important;
          height: auto !important;
        }

        .stats {
          background: white !important;
          border: 1px solid #ddd !important;
          break-inside: avoid !important;
          page-break-inside: avoid !important;
          backdrop-filter: none !important;
        }

        .stat-item {
          border: 1px solid #ddd !important;
          background-color: white !important;
        }

        table {
          width: 100% !important;
          page-break-inside: auto !important;
          border-collapse: collapse !important;
          font-size: 10pt !important;
          margin-bottom: 20px !important;
        }

        th,
        td {
          padding: 6px 4px !important;
          font-size: 9pt !important;
          border: 1px solid #ddd !important;
        }

        tr {
          page-break-inside: avoid !important;
          page-break-after: auto !important;
        }

        thead {
          display: table-header-group !important;
        }

        tfoot {
          display: table-footer-group !important;
        }

        .signature-section {
          break-inside: avoid !important;
          page-break-inside: avoid !important;
          break-before: auto !important;
          margin-top: 30px !important;
        }

        .footer {
          position: static !important;
          bottom: auto !important;
          width: 100% !important;
          text-align: center !important;
          margin-top: 20px !important;
        }

        /* Ensure colors print correctly */
        th {
          background-color: #3498db !important;
          color: white !important;
          -webkit-print-color-adjust: exact !important;
        }

        .averages-row {
          background-color: #eaf2f8 !important;
          -webkit-print-color-adjust: exact !important;
        }

        .average-label {
          background-color: #3498db !important;
          color: white !important;
          -webkit-print-color-adjust: exact !important;
        }

        .class-average-row {
          background-color: #d4efdf !important;
          -webkit-print-color-adjust: exact !important;
        }

        .class-average-row .average-label {
          background-color: #27ae60 !important;
          color: white !important;
          -webkit-print-color-adjust: exact !important;
        }

        /* Premium print color preservation */
        .header {
          background: linear-gradient(
            135deg,
            #1e3c72 0%,
            #2a5298 50%,
            #4a90e2 100%
          ) !important;
        }

        .class-info-item {
          background: rgba(255, 255, 255, 0.9) !important;
        }

        .performance-item.exceeding {
          background: linear-gradient(
            135deg,
            #d4edda 0%,
            #c3e6cb 100%
          ) !important;
        }

        .performance-item.meeting {
          background: linear-gradient(
            135deg,
            #fff3cd 0%,
            #ffeaa7 100%
          ) !important;
        }

        .performance-item.approaching {
          background: linear-gradient(
            135deg,
            #f8d7da 0%,
            #f5c6cb 100%
          ) !important;
        }

        .performance-item.below {
          background: linear-gradient(
            135deg,
            #f5c6cb 0%,
            #f1b0b7 100%
          ) !important;
        }
      }
    </style>
    <script>
      function confirmDelete(grade, stream, term, assessmentType) {
        // Set up the delete form action
        const deleteForm = document.getElementById("deleteForm");
        deleteForm.action = `/classteacher/delete_marksheet/${grade}/${stream}/${term}/${assessmentType}`;

        // Update the confirmation message
        const deleteMessage = document.getElementById("deleteMessage");
        deleteMessage.textContent = `Are you sure you want to delete all marks for ${grade} ${stream} in ${term} ${assessmentType}? This action cannot be undone.`;

        // Show the modal
        const modal = document.getElementById("deleteModal");
        modal.style.display = "block";
      }

      function closeModal() {
        // Hide the modal
        const modal = document.getElementById("deleteModal");
        modal.style.display = "none";
      }

      // Close the modal if the user clicks outside of it
      window.onclick = function (event) {
        const modal = document.getElementById("deleteModal");
        if (event.target == modal) {
          modal.style.display = "none";
        }
      };

      // Function to automatically adjust logo styling based on image properties
      function adjustLogoStyle(img) {
        if (!img || !img.complete) return;

        const width = img.naturalWidth;
        const height = img.naturalHeight;
        const aspectRatio = width / height;

        // Remove existing size classes
        img.classList.remove("small", "medium", "large");
        img.classList.remove("circular", "square", "rounded");

        // Determine size based on image dimensions
        const maxDimension = Math.max(width, height);
        if (maxDimension <= 200) {
          img.classList.add("small");
        } else if (maxDimension <= 400) {
          img.classList.add("medium");
        } else {
          img.classList.add("large");
        }

        // Determine shape based on aspect ratio
        if (Math.abs(aspectRatio - 1) < 0.1) {
          // Nearly square (aspect ratio close to 1:1)
          if (isCircularLogo(img)) {
            img.classList.add("circular");
          } else {
            img.classList.add("rounded");
          }
        } else {
          // Rectangular
          img.classList.add("square");
        }

        // Adjust for very wide or very tall logos
        if (aspectRatio > 2) {
          // Very wide logo
          img.style.maxWidth = "120px";
          img.style.maxHeight = "40px";
        } else if (aspectRatio < 0.5) {
          // Very tall logo
          img.style.maxWidth = "40px";
          img.style.maxHeight = "80px";
        }

        // Add loading animation completion
        img.style.opacity = "1";
        img.style.transform = "scale(1)";
      }

      // Helper function to detect if logo appears circular
      function isCircularLogo(img) {
        // This is a simple heuristic - in practice, you might want more sophisticated detection
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");
        canvas.width = img.naturalWidth;
        canvas.height = img.naturalHeight;

        try {
          ctx.drawImage(img, 0, 0);
          // Simple check: if corners are mostly transparent/white, likely circular
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
          const data = imageData.data;

          // Check corner pixels for transparency/whiteness
          const corners = [
            [0, 0], // top-left
            [canvas.width - 1, 0], // top-right
            [0, canvas.height - 1], // bottom-left
            [canvas.width - 1, canvas.height - 1], // bottom-right
          ];

          let transparentCorners = 0;
          corners.forEach(([x, y]) => {
            const index = (y * canvas.width + x) * 4;
            const alpha = data[index + 3];
            const r = data[index];
            const g = data[index + 1];
            const b = data[index + 2];

            // Check if corner is transparent or very light
            if (alpha < 50 || (r > 240 && g > 240 && b > 240)) {
              transparentCorners++;
            }
          });

          return transparentCorners >= 3; // If 3+ corners are transparent/white, likely circular
        } catch (e) {
          // If canvas operations fail (CORS, etc.), default to rounded
          return false;
        }
      }

      // Function to show logo fallback when image fails to load
      function showLogoFallback(img) {
        const logoWrapper = img.parentElement;
        const schoolName = "{{ school_info.school_name|default('SCHOOL') }}";
        const fallbackLetter = schoolName.charAt(0).toUpperCase();

        // Hide the failed image
        img.style.display = "none";

        // Create and show fallback
        const fallback = document.createElement("div");
        fallback.className = "logo-fallback";
        fallback.textContent = fallbackLetter;
        fallback.id = "logoFallback";

        logoWrapper.appendChild(fallback);
      }

      // Initialize logo styling on page load
      document.addEventListener("DOMContentLoaded", function () {
        const logo = document.getElementById("schoolLogo");
        const fallback = document.getElementById("logoFallback");

        if (logo) {
          // Add initial loading styles
          logo.style.opacity = "0";
          logo.style.transform = "scale(0.8)";
          logo.style.transition = "all 0.3s ease";

          if (logo.complete) {
            adjustLogoStyle(logo);
          }
        }

        if (fallback) {
          // Animate fallback appearance
          fallback.style.opacity = "0";
          fallback.style.transform = "scale(0.8)";
          fallback.style.transition = "all 0.3s ease";

          setTimeout(() => {
            fallback.style.opacity = "1";
            fallback.style.transform = "scale(1)";
          }, 100);
        }
      });

      // Function to toggle printing instructions
      function toggleInstructions() {
        const instructionsPanel = document.getElementById("instructions-panel");
        if (instructionsPanel.style.display === "none") {
          instructionsPanel.style.display = "block";
        } else {
          instructionsPanel.style.display = "none";
        }
      }

      // Close instructions panel when clicking outside of it
      document.addEventListener("click", function (event) {
        const instructionsPanel = document.getElementById("instructions-panel");
        const instructionsToggle = document.querySelector(
          ".instructions-toggle"
        );

        if (
          instructionsPanel.style.display === "block" &&
          !instructionsPanel.contains(event.target) &&
          event.target !== instructionsToggle
        ) {
          instructionsPanel.style.display = "none";
        }
      });

      // Functions for subject selection
      function selectAllSubjects() {
        const checkboxes = document.querySelectorAll(
          'input[type="checkbox"][id^="include_subject_"]'
        );
        checkboxes.forEach((checkbox) => {
          checkbox.checked = true;
        });
      }

      function deselectAllSubjects() {
        const checkboxes = document.querySelectorAll(
          'input[type="checkbox"][id^="include_subject_"]'
        );
        checkboxes.forEach((checkbox) => {
          checkbox.checked = false;
        });
      }

      function printCleanReport() {
        // Simply use the browser's built-in print functionality with clean CSS
        window.print();
      }

      function downloadReportAsPDF() {
        // Alternative method: Use the browser's built-in print to PDF
        window.print();
      }
    </script>
  </head>
  <body>
    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %} {% if
    messages %}
    <div
      class="flash-messages"
      style="
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 9999;
        width: 90%;
        max-width: 600px;
      "
    >
      {% for category, message in messages %}
      <div
        class="alert alert-{{ category }}"
        style="
              padding: 15px 20px;
              margin-bottom: 10px;
              border-radius: 8px;
              font-weight: 500;
              box-shadow: 0 4px 12px rgba(0,0,0,0.15);
              display: flex;
              justify-content: space-between;
              align-items: center;
              animation: slideDown 0.3s ease-out;
              {% if category == 'success' %}
                background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
                color: #155724;
                border: 1px solid #c3e6cb;
              {% elif category == 'error' %}
                background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
                color: #721c24;
                border: 1px solid #f5c6cb;
              {% elif category == 'warning' %}
                background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
                color: #856404;
                border: 1px solid #ffeaa7;
              {% else %}
                background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
                color: #0c5460;
                border: 1px solid #bee5eb;
              {% endif %}
            "
      >
        <span>{{ message }}</span>
        <button
          onclick="this.parentElement.style.display='none'"
          style="
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: inherit;
            opacity: 0.7;
            padding: 0;
            margin-left: 15px;
          "
        >
          &times;
        </button>
      </div>
      {% endfor %}
    </div>
    <style>
      @keyframes slideDown {
        from {
          opacity: 0;
          transform: translateX(-50%) translateY(-20px);
        }
        to {
          opacity: 1;
          transform: translateX(-50%) translateY(0);
        }
      }

      /* Auto-hide success messages after 5 seconds */
      .alert-success {
        animation: slideDown 0.3s ease-out, fadeOut 0.5s ease-out 4.5s forwards;
      }

      @keyframes fadeOut {
        to {
          opacity: 0;
          transform: translateX(-50%) translateY(-20px);
        }
      }
    </style>
    <script>
      // Auto-hide success messages after 5 seconds
      setTimeout(function () {
        const successAlerts = document.querySelectorAll(".alert-success");
        successAlerts.forEach(function (alert) {
          alert.style.display = "none";
        });
      }, 5000);
    </script>
    {% endif %} {% endwith %}

    <div class="report-container">
      <div class="action-buttons">
        <a href="{{ url_for('classteacher.dashboard') }}" class="back-btn"
          >Back to Dashboard</a
        >
        <a
          href="{{ url_for('classteacher.edit_class_marks', grade=grade, stream=stream, term=term, assessment_type=assessment_type) }}"
          class="edit-btn"
          >Edit Marks</a
        >
        <button
          onclick="confirmDelete('{{ grade }}', '{{ stream }}', '{{ term }}', '{{ assessment_type }}')"
          class="delete-btn"
        >
          Delete Marksheet
        </button>
      </div>

      <!-- Subject Selection Form -->
      <div
        class="subject-selection-form"
        style="
          margin-bottom: 20px;
          padding: 15px;
          background-color: #f8f9fa;
          border-radius: 8px;
          border-left: 5px solid #3498db;
        "
      >
        <h3
          style="
            margin-top: 0;
            color: #2c3e50;
            font-size: 18px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
          "
        >
          Select Subjects to Include in Report
        </h3>
        <form
          method="POST"
          action="{{ url_for('classteacher.preview_class_report', grade=grade, stream=stream, term=term, assessment_type=assessment_type) }}"
        >
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />
          <div
            style="
              display: grid;
              grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
              gap: 10px;
              margin-top: 15px;
            "
          >
            {% for subject in subjects %}
            <div style="display: flex; align-items: center">
              <input
                type="checkbox"
                id="include_subject_{{ loop.index0 }}"
                name="include_subject_{{ loop.index0 }}"
                value="{{ subject.id if subject is defined and subject.id is defined else loop.index }}"
                {%
                if
                not
                session.selected_subjects
                or
                subject.id
                in
                session.selected_subjects
                %}checked{%
                endif
                %}
                style="margin-right: 8px"
              />
              <label
                for="include_subject_{{ loop.index0 }}"
                style="font-size: 14px"
                >{{ subject.name }}</label
              >
            </div>
            {% endfor %}
          </div>
          <div style="margin-top: 15px; display: flex; gap: 10px">
            <button
              type="button"
              onclick="selectAllSubjects()"
              style="
                padding: 8px 12px;
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
              "
            >
              Select All
            </button>
            <button
              type="button"
              onclick="deselectAllSubjects()"
              style="
                padding: 8px 12px;
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
              "
            >
              Deselect All
            </button>
            <button
              type="submit"
              style="
                padding: 8px 12px;
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                margin-left: auto;
              "
            >
              Update Report
            </button>
          </div>
        </form>
      </div>

      <!-- Print-Friendly Controls -->
      <div class="print-controls">
        <button onclick="printCleanReport()" class="print-btn">
          <i class="print-icon">🖨️</i> Print/Download Report
        </button>
        <button
          onclick="downloadReportAsPDF()"
          class="print-btn"
          style="background-color: #28a745"
        >
          <i class="print-icon">📄</i> Quick Download
        </button>
        <a
          href="{{ url_for('classteacher.view_student_reports', grade=grade, stream=stream, term=term, assessment_type=assessment_type) }}"
          style="
            padding: 10px 15px;
            background-color: #28a745;
            color: white;
            border-radius: 5px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
          "
        >
          <i style="font-style: normal">👨‍👩‍👧‍👦</i> View Individual Reports
        </a>
        <div class="print-instructions">
          <button onclick="toggleInstructions()" class="instructions-toggle">
            <i class="info-icon">ℹ️</i> Printing Instructions
          </button>
          <div
            id="instructions-panel"
            class="instructions-panel"
            style="display: none"
          >
            <h4>How to Print/Save This Report:</h4>
            <ol>
              <li>
                Click the <strong>Print Report</strong> button to open your
                browser's print dialog.
              </li>
              <li>
                Select your printer or choose "Save as PDF" to create a PDF
                file.
              </li>
              <li>
                For best results, use landscape orientation and enable
                background colors.
              </li>
              <li>Set margins to "Narrow" or "None" for the best layout.</li>
            </ol>
          </div>
        </div>
      </div>

      <!-- Delete Confirmation Modal -->
      <div id="deleteModal" class="modal">
        <div class="modal-content">
          <h3>Confirm Deletion</h3>
          <p id="deleteMessage">
            Are you sure you want to delete this marksheet? This action cannot
            be undone.
          </p>
          <div class="modal-buttons">
            <form id="deleteForm" method="POST" action="">
              <input
                type="hidden"
                name="csrf_token"
                value="{{ csrf_token() }}"
              />
              <button type="button" onclick="closeModal()" class="cancel-btn">
                Cancel
              </button>
              <button type="submit" class="confirm-delete-btn">Delete</button>
            </form>
          </div>
        </div>
      </div>
      <div class="header">
        <div class="header-left">
          <div class="logo-wrapper">
            {% if school_info.logo_filename %}
            <img
              src="{{ url_for('static', filename='uploads/logos/' + school_info.logo_filename) }}"
              alt="{{ school_info.school_name|default('School') }} Logo"
              class="header-logo"
              id="schoolLogo"
              onerror="showLogoFallback(this)"
              onload="adjustLogoStyle(this)"
            />
            {% else %}
            <!-- Temporary: Use the known Hillview logo file -->
            <img
              src="{{ url_for('static', filename='uploads/logos/optimized_school_logo_1750595986_hvs.jpg') }}"
              alt="{{ school_info.school_name|default('School') }} Logo"
              class="header-logo"
              id="schoolLogo"
              onerror="showLogoFallback(this)"
              onload="adjustLogoStyle(this)"
            />
            {% endif %}
          </div>
          <div class="school-info">
            <div class="school-name">
              {{ school_info.school_name|default('HILLVIEW SCHOOL') }}
            </div>
            <div class="school-details">
              {{ school_info.school_address|default('Kiambu Road, Ruaka Estate,
              Ruiru') }} | {{ school_info.school_phone|default('+254705204870')
              }} | {{
              school_info.school_email|default('<EMAIL>') }}
            </div>
          </div>
        </div>
        <div class="header-right">{{ education_level.upper() }} MARKSHEET</div>
      </div>

      <div class="class-info-section">
        <div class="class-info-left">
          <div class="class-info-item">
            <span class="class-info-label">GRADE:</span>
            <span class="class-info-value"
              >{{ grade.replace('Grade ', '') }}</span
            >
          </div>
          <div class="class-info-item">
            <span class="class-info-label">STREAM:</span>
            <span class="class-info-value"
              >{{ stream.replace('Stream ', '') }}</span
            >
          </div>
          <div class="class-info-item">
            <span class="class-info-label">TERM:</span>
            <span class="class-info-value"
              >{{ term.replace('_', ' ').upper() }}</span
            >
          </div>
          <div class="class-info-item">
            <span class="class-info-label">ASSESSMENT:</span>
            <span class="class-info-value">{{ assessment_type.upper() }}</span>
          </div>
        </div>
      </div>

      <!-- Table wrapper for horizontal scroll on very small screens -->
      <div style="overflow-x: auto; -webkit-overflow-scrolling: touch">
        <table>
          <thead>
            <tr>
              <th>S/N</th>
              <th>STUDENT NAME</th>
              {% for subject_name in subject_names %} {% set subject_obj =
              filtered_subjects | selectattr('name', 'equalto', subject_name) |
              first %} {% if subject_obj and subject_obj.is_composite %}
              <!-- Main composite subject header -->
              <th
                class="subject-header composite-main-header"
                colspan="{{ subject_components.get(subject_name, [])|length }}"
              >
                {{ subject_name.upper() }}
              </th>
              {% else %}
              <th class="subject-header">
                {{ abbreviated_subjects[loop.index0] }}
              </th>
              {% endif %} {% endfor %}
              <th>TOTAL</th>
              <th>AVG %</th>
              <th style="background: #4a90e2; color: white">GRD</th>
              <th style="background: #f0ad4e; color: white">RANK</th>
            </tr>
            <!-- Second header row for composite subject components -->
            <tr>
              <th></th>
              <!-- Empty for S/N -->
              <th></th>
              <!-- Empty for STUDENT NAME -->
              {% for subject_name in subject_names %} {% set subject_obj =
              filtered_subjects | selectattr('name', 'equalto', subject_name) |
              first %} {% if subject_obj and subject_obj.is_composite %} {% set
              components = subject_components.get(subject_name, []) %} {% for
              component in components %}
              <th class="component-header">{{ component.name[:4].upper() }}</th>
              {% endfor %} {% else %}
              <th></th>
              <!-- Empty for regular subjects -->
              {% endif %} {% endfor %}
              <th></th>
              <!-- Empty for TOTAL -->
              <th></th>
              <!-- Empty for AVG % -->
              <th></th>
              <!-- Empty for GRD -->
              <th></th>
              <!-- Empty for RANK -->
            </tr>
          </thead>
          <tbody>
            {% for student_data in class_data %}
            <tr class="student-row">
              <td>{{ student_data.index }}</td>
              <td>{{ student_data.student.upper() }}</td>
              {% for subject_name in subject_names %} {% set subject_obj =
              filtered_subjects | selectattr('name', 'equalto', subject_name) |
              first %} {% if subject_obj and subject_obj.is_composite %} {% set
              components = subject_components.get(subject_name, []) %} {% set
              student_id = student_data.get('student_id', 0) %} {% for component
              in components %}
              <td style="font-size: 12px">
                {{ component_marks_data.get(student_id, {}).get(subject_name,
                {}).get(component.name, 0) | int }}
              </td>
              {% endfor %}
              <td style="background-color: #f0f8ff; font-weight: bold">
                {{ student_data.filtered_marks.get(subject_name, 0) | int }}
              </td>
              {% else %}
              <td>
                {{ student_data.filtered_marks.get(subject_name, 0) | int }}
              </td>
              {% endif %} {% endfor %}
              <td class="total-cell">
                {{ student_data.filtered_total | int }}/{{
                student_data.total_possible_marks }}
              </td>
              <td class="avg-cell">
                {{ student_data.filtered_average | round(0) }}
              </td>
              <td class="grade-cell">
                {{ student_data.performance_category }}
              </td>
              <td class="rank-cell">{{ student_data.rank }}</td>
            </tr>
            {% endfor %}

            <!-- Subject Averages Row -->
            <tr class="averages-row">
              <td colspan="2" class="average-label">SUBJECT AVERAGES</td>
              {% for subject_name in subject_names %} {% set subject_obj =
              filtered_subjects | selectattr('name', 'equalto', subject_name) |
              first %} {% if subject_obj and subject_obj.is_composite %} {% set
              components = subject_components.get(subject_name, []) %} {% for
              component in components %}
              <td class="subject-average" style="font-size: 11px">
                {{ component_averages.get(subject_name, {}).get(component.name,
                0) }}
              </td>
              {% endfor %}
              <td class="subject-average" style="background-color: #d6eaf8">
                {{ subject_averages.get(subject_name, 0) | round(2) }}
              </td>
              {% else %}
              <td class="subject-average">
                {{ subject_averages.get(subject_name, 0) | round(2) }}
              </td>
              {% endif %} {% endfor %}
              <td colspan="4" class="empty-cell"></td>
            </tr>

            <!-- Class Average Row -->
            <tr class="averages-row class-average-row">
              <td colspan="2" class="average-label">CLASS AVERAGE</td>
              <td colspan="{{ subject_names|length }}" class="empty-cell"></td>

              <!-- Class Average Total -->
              <td class="total-average">{{ class_average | round(2) }}</td>

              <!-- Class Average Percentage -->
              {% set avg_percentage_total = 0 %} {% set avg_student_count = 0 %}
              {% for student_data in class_data %} {% if
              student_data.filtered_average > 0 %} {% set avg_percentage_total =
              avg_percentage_total + student_data.filtered_average %} {% set
              avg_student_count = avg_student_count + 1 %} {% endif %} {% endfor
              %}
              <td class="avg-cell">
                {% if avg_student_count > 0 %} {{ (avg_percentage_total /
                avg_student_count) | round(2) }} {% else %} 0 {% endif %}
              </td>

              <td colspan="2" class="empty-cell"></td>
            </tr>
          </tbody>
        </table>
      </div>
      <!-- Close table wrapper -->

      <div class="performance-summary">
        <h3>Performance Summary</h3>
        <div class="performance-grid">
          <div class="performance-item exceeding">
            <span>EE1/EE2 (Exceeding Expectation, ≥75%)</span>
            <strong>{{ stats.exceeding }} learners</strong>
          </div>
          <div class="performance-item meeting">
            <span>ME1/ME2 (Meeting Expectation, 41-74%)</span>
            <strong>{{ stats.meeting }} learners</strong>
          </div>
          <div class="performance-item approaching">
            <span>AE1/AE2 (Approaching Expectation, 21-40%)</span>
            <strong>{{ stats.approaching }} learners</strong>
          </div>
          <div class="performance-item below">
            <span>BE1/BE2 (Below Expectation, <21%)</span>
            <strong>{{ stats.below }} learners</strong>
          </div>
        </div>
      </div>

      <!-- Subject Teachers Section -->
      {% if staff_info and staff_info.subject_teachers %}
      <div class="subject-teachers-section">
        <h3>Subject Teachers</h3>
        <div class="teachers-grid">
          {% for subject, teacher in staff_info.subject_teachers.items() %}
          <div class="teacher-info">
            <strong>{{ subject }}:</strong> {{ teacher.name }} {% if
            teacher.qualification %} <br /><small
              >{{ teacher.qualification }}</small
            >
            {% endif %}
          </div>
          {% endfor %}
        </div>
      </div>
      {% endif %}

      <div class="signature-section">
        {% if visibility.show_class_teacher %}
        <div class="signature-item">
          <strong>Class Teacher</strong>
          <div class="signature-line"></div>
          {% if staff_info and staff_info.class_teacher %} {{
          staff_info.class_teacher.name }} {% else %} Not Assigned {% endif %}
        </div>
        {% endif %} {% if visibility.show_deputy_headteacher %}
        <div class="signature-item">
          <strong>Deputy Head Teacher</strong>
          <div class="signature-line"></div>
          {% if staff_info and staff_info.deputy_headteacher %} {{
          staff_info.deputy_headteacher.name }} {% else %} Not Assigned {% endif
          %}
        </div>
        {% endif %} {% if visibility.show_headteacher %}
        <div class="signature-item">
          <strong>Head Teacher</strong>
          <div class="signature-line"></div>
          {% if staff_info and staff_info.headteacher %} {{
          staff_info.headteacher.name }} {% else %} Head Teacher {% endif %}
        </div>
        {% endif %}
      </div>

      <div class="footer">
        <p>Generated on: {{ current_date }}</p>
        <p>
          {{ school_info.school_name|default('School Management System') }} | {{
          school_info.report_footer|default('Powered by CbcTeachkit') }}
        </p>
      </div>
    </div>
  </body>
</html>
