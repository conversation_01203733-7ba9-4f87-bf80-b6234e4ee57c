venv/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Database files
*.db
*.sqlite
*.sqlite3
kirima_primary.db*

# Log files
logs/
*.log

# PDF reports
*.pdf
report_*.pdf
class_report_*.pdf
scoresheet_*.pdf

# Build and distribution
build/
dist/
*.egg-info/
*.spec

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db
*.stackdump

# Temporary files
,
*.tmp
*.temp

# Backup files
*.backup*

# Test files
test_*.py
*_test.py

# Cache directories
cache/
__pycache__/

# Executable files
*.exe

# Additional temporary files
*.bak
*.old

# Distribution packages
distribution/
create_update_package.py
